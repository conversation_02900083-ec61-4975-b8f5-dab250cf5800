package com.juzifenqi.plus.external.config;//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//


import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableConfigurationProperties({CalmOpenApiProperties.class})
//@ComponentScan({"com.juzishuke.calm.open"})
public class CalmOpenApiConfig {
    public CalmOpenApiConfig() {
    }
}

package com.juzifenqi.plus.external;

import com.juzifenqi.member.MemberResult;
import com.juzifenqi.member.entity.member.MemberInfo;
import com.juzifenqi.member.v2.api.MemberQueryApi;
import com.juzifenqi.member.v2.api.MemberVerifyApi;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 用户
 *
 * <AUTHOR>
 * @date 2023/9/14 11:28
 **/
@Component
public class MemberExternal {

    @Autowired
    private MemberQueryApi memberQueryApi;

    @Autowired
    private MemberVerifyApi memberVerifyApi;

    public MemberResult<MemberInfo> getMemberById(Integer userId) {
        return memberQueryApi.getMemberById(userId);
    }

    public MemberResult<List<MemberInfo>> getMemberByIds(List<Integer> userIds) {
        return memberQueryApi.getMemberByIds(userIds);
    }

    public MemberResult<MemberInfo> getMemberByMobile(String mobile, Integer channelId) {
        return memberQueryApi.getMemberByMobile(mobile, channelId);
    }

    public MemberResult<Boolean> checkVipByUid(Integer userId) {
        return memberVerifyApi.checkVipByUid(userId);
    }
}

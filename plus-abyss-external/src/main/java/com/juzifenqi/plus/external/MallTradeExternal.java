package com.juzifenqi.plus.external;

import com.juzifenqi.alita.result.BaseResult;
import com.juzifenqi.trade.api.IMallTradeService;
import com.juzifenqi.trade.dto.OrderSuccessDTO;
import com.juzifenqi.trade.dto.data.OrderCommitDTO;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/3/30 18:18
 **/
@Component
public class MallTradeExternal {

    @Resource
    private IMallTradeService mallTradeService;

    public BaseResult<OrderSuccessDTO> commitOrderInfo(OrderCommitDTO commitDTO) {
        return mallTradeService.commitOrderInfo(commitDTO);
    }

}

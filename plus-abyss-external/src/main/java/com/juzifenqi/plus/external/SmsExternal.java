package com.juzifenqi.plus.external;


import com.juzi.sms.ResponseDTO;
import com.juzi.sms.api.JuziSmsV2Api;
import com.juzi.smsgroup.vo.SmsMsgV2Dto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 短信
 *
 * <AUTHOR>
 * @date 2023/9/14 10:37
 **/
@Component
public class SmsExternal {

    @Autowired
    private JuziSmsV2Api juziSmsV2Api;

    /**
     * <AUTHOR>
     * @date 2023/9/14 10:38
     **/
    public ResponseDTO sendMsg(SmsMsgV2Dto smsMsgV2Dto) {
        return juziSmsV2Api.sendMsg(smsMsgV2Dto);
    }
}

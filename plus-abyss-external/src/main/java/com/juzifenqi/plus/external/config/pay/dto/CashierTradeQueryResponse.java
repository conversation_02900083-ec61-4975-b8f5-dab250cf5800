package com.juzifenqi.plus.external.config.pay.dto;

import lombok.Data;

/**
 * 收银台支付结果查询
 */
@Data
public class CashierTradeQueryResponse {
    //支付状态: S成功 F失败 I处理中 C已关闭 W待处理
    private String state;
    //支付产品编码
    private String payProductCode;
    //错误码
    private String errorCode;
    //错误信息
    private String errorMsg;
    //交易时间
    private String tradeTime;
    //支付宝是否已签约
    private boolean alipaySignStatus;
    //绑卡id
    private String bankCardId;
    //收银台流水号
    private String serialNumber;
    //渠道请求流水号
    private String thirdPayNum;
    //订单号
    private String orderId;
    //支付流水号
    private String paySerialNumber;
}

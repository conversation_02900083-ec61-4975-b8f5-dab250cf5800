package com.juzifenqi.plus.external.config.pay.dto;//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//


import lombok.Data;

@Data
public class ConfirmPayResp {
    private String returnUrl;
    private String payInfo;
    private String state;
    private String tradeNo;
    private String message = "支付完成，请等待具体支付结果！";
    private String code;
    private String channelProductNo;
    private String serialNumber;

}

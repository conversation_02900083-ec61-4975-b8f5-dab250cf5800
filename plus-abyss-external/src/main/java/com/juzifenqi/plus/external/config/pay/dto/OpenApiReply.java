package com.juzifenqi.plus.external.config.pay.dto;//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//


import com.juzishuke.framework.common.enums.TeamEnum;
import com.juzishuke.framework.common.response.CodeEnum;

public enum OpenApiReply implements CodeEnum {
    OPEN_REQ_DATA_FAIL(911L, "请求参数格式不符合JSON数据规范"),
    OPEN_RES_DATA_FAIL(922L, "响应参数格式不符合JSON数据规范"),
    OPEN_CONNECT_TIME_OUT(933L, "连接calm服务超时"),
    OPEN_SOCKET_TIME_OUT(944L, "calm响应超时"),
    OPEN_CONNECT_FAIL(955L, "连接calm服务失败"),
    OPEN_NO_RESULT(960L, "calm服务响应对象为空"),
    OPEN_INACTIVITY_CANCEL(965L, "没有可用活动连接数"),
    OPEN_UNKNOWN_ERROR(999L, "请求calm服务系统未知异常");

    private final long code;
    private final String message;

    private OpenApiReply(long code, String message) {
        this.code = code;
        this.message = message;
    }

    public long getCode() {
        return this.code;
    }

    public String getMessage() {
        return this.message;
    }

    public TeamEnum getTeam() {
        return TeamEnum.FINANCE;
    }

    public int getModuleCode() {
        return 999;
    }
}

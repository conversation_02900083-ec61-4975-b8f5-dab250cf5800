package com.juzifenqi.plus.external.config.pay.dto;

import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/10/30
 */
@Data
public class TradeFlow {

    private String state;
    private String tradeNo;
    private String errorCode;
    private String errorMsg;
    private Long   payErrorCode;
    private String payErrorMsg;
    private String payChannel;
    private Date   tradeTime;
    private String ctCardNo;
}

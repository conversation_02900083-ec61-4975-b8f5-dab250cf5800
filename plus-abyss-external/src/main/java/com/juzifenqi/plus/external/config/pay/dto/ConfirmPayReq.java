package com.juzifenqi.plus.external.config.pay.dto;//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//


import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class ConfirmPayReq {
    private String appid;
    private Long bankCardId;
    private String payProductCode;
    private String application;
    private String businessScene;
    private String customerId;
    private String thirdPayNum;
    private String tradeType;
    private String source;
    private BigDecimal totalAmount;
    private String returnUrl;
    private String notifyUrl;
    private String orderId;
    private String tradeName;
    private List<Divide> splitInfo;


}

package com.juzifenqi.plus.external.config;//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//


import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties("calm.client")
public class CalmOpenApiProperties {
    private String appId;
    private String serverAddr;

    public CalmOpenApiProperties() {
    }

    public String getAppId() {
        return this.appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getServerAddr() {
        return this.serverAddr;
    }

    public void setServerAddr(String serverAddr) {
        this.serverAddr = serverAddr;
    }
}

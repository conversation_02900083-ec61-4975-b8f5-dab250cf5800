package com.juzifenqi.plus.external.config.pay.dto;

import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/10/30
 */
@Data
public class UserCardListResp {

    List<UserCardDetail> userCardDetailList;

    @Data
    public static class UserCardDetail {

        private String  cardNo;
        private String  phoneNumber;
        private String  certNo;
        private String  customerName;
        private String  picture;
        private String  backImg;
        private String  customerId;
        private Long    id;
        private String  bankName;
        private String  isDefault;
        private String  isSupport;
        private String  paySupport;
        private String  bankCode;
        private String  protocolNumber;
        private String  deductionAuth;
        private String  mobile;
        private String  payChannel;
        private String  rebind;
        private Date    createTime;
        private String  thirdBindStatus;
        private String  application;
        private String  ctCardNo;
        private String  ctPhoneNumber;
        private String  ctCertNo;
        private String  merchantId;
        private String  capitalBindState;
        private Integer state;
        private Long    bankCodeId;
    }
}

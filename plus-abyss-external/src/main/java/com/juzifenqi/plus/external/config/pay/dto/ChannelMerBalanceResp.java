package com.juzifenqi.plus.external.config.pay.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
/**
 * Description: 渠道支付商户余额查询响应参数
 *
 * <AUTHOR>
 * @date created on 2025/6/20
 */
@Data
public class ChannelMerBalanceResp {

    /**
     * 支付通道商户号
     */
    private String channelMerchant;

    /**
     * 支付通道编码
     */
    private String payChannel;

    /**
     * 账户总额
     */
    private BigDecimal totalAmount;

    /**
     * 可用余额
     */
    private String availableAmount;

    /**
     * 冻结金额
     */
    private String freezeAmount;

    /**
     * 待结算金额
     */
    private String settleAmount;

}

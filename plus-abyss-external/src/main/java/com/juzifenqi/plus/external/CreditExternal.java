package com.juzifenqi.plus.external;

import com.alibaba.fastjson.JSONObject;
import com.juzishuke.credit.api.CreditChannelProvider;
import com.juzishuke.credit.api.CreditQueryProvider;
import com.juzishuke.credit.enums.OperationEnum;
import com.juzishuke.credit.request.CreditCalculateRequest;
import com.juzishuke.credit.request.CustomerCreditChannelDetailRequest;
import com.juzishuke.credit.request.CustomerCreditChannelStateChangeRequest;
import com.juzishuke.credit.vo.CreditCalculateVO;
import com.juzishuke.credit.vo.CustomerCreditChannelDetailVO;
import com.juzishuke.framework.common.response.BaseResponse;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 额度
 *
 * <AUTHOR>
 * @date 2023/9/14 11:11
 **/
@Component
@Slf4j
public class CreditExternal {

    @Autowired
    private CreditQueryProvider   creditQueryProvider;
    @Resource
    private CreditChannelProvider creditChannelProvider;

    public CustomerCreditChannelDetailVO getCustomerCreditChannelDetail(Integer userId,
            boolean showDetail) {
        CustomerCreditChannelDetailRequest request = new CustomerCreditChannelDetailRequest();
        request.setCustomerId(userId);
        request.setShowDetail(showDetail);
        return creditQueryProvider.getCustomerCreditChannelDetail(request).getData();
    }

    /**
     * 冻结/解冻子账号接口,
     *
     * @param userId 用户ID
     * @param unfree true-解冻账户，false-冻结账户
     * @param reason 原因
     * @return {@link boolean}  true 成功，false 失败
     * <AUTHOR>
     * @date 2022/1/15
     */
    public Boolean changeCustomerChannelAccountState(Integer userId, Boolean unfree,
            String reason) {
        try {
            CustomerCreditChannelStateChangeRequest request = new CustomerCreditChannelStateChangeRequest();
            request.setCustomerId(userId);
            request.setOperationEnum(unfree ? OperationEnum.UNFREEZE : OperationEnum.FREEZE);
            request.setRequestNumber(
                    System.currentTimeMillis() + "_" + userId + "_" + (int) ((Math.random() * 9 + 1)
                            * 100000));
            request.setReason(reason);
            log.info("调用额度2.0 冻结/解冻子账号接口，入参：{}", JSONObject.toJSONString(request));
            BaseResponse<Boolean> response = creditChannelProvider.changeCustomerChannelAccountState(
                    request);
            log.info("调用额度2.0 冻结/解冻子账号接口，返回：{}", JSONObject.toJSONString(response));
            if (response != null && response.isSuccess()) {
                return response.getData();
            }
        } catch (Exception e) {
            log.error("调用额度2.0 冻结/解冻子账号接口异常", e);
        }
        return false;
    }

    /**
     * 提额额度试算接口
     */
    public BaseResponse<CreditCalculateVO> creditCalculateSimulation(CreditCalculateRequest req) {
        return creditQueryProvider.creditCalculateSimulation(req);
    }
}

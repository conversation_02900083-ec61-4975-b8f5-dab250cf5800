//package com.juzifenqi.plus.external.config;
//
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.annotation.ComponentScan;
//import org.springframework.context.annotation.Configuration;
//
///**
// * 新支付系统配置类
// * <AUTHOR>
// * @Date 2024/9/4
// */
//@Configuration
//@ComponentScan("com.juzishuke.calm.open")
//public class PayExternalConfig {
//
//    /**
//     * 仅用来声明新支付系统配置参数
//     */
//    @Value("${calm.client.appId}")
//    String appId;
//
//    /**
//     * 803支付代理服务地址
//     */
//    @Value("${calm.client.serverAddr}")
//    String serverAddr;
//}

package com.juzifenqi.plus.external.config;//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//


import java.io.IOException;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;

public class OkHttpUtil {
    private static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");
    private static final String DEFAULT_EMPTY = "";
    private static OkHttpClient okHttpClient;

    public OkHttpUtil() {
    }

    public static String postJson(String url, Map<String, String> headers, String param) throws IOException {
        RequestBody requestBody = RequestBody.create(JSON, param);
        Request request = (new Request.Builder()).url(url).headers(Headers.of(headers)).post(requestBody).build();
        Response execute = okHttpClient.newCall(request).execute();
        if (execute.isSuccessful()) {
            ResponseBody responseBody = execute.body();
            if (null != responseBody) {
                return responseBody.string();
            }
        }

        return "";
    }

    static {
        if (null == okHttpClient) {
            Class var0 = OkHttpUtil.class;
            synchronized(OkHttpUtil.class) {
                if (null == okHttpClient) {
                    okHttpClient = (new OkHttpClient.Builder()).connectTimeout(500L, TimeUnit.MILLISECONDS).readTimeout(15L, TimeUnit.SECONDS).writeTimeout(3L, TimeUnit.SECONDS).retryOnConnectionFailure(true).build();
                }
            }
        }

    }
}

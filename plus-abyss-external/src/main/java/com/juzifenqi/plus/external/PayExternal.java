package com.juzifenqi.plus.external;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.juzifenqi.plus.external.config.AbstractCallContent;
import com.juzifenqi.plus.external.config.DoCallService;
import com.juzifenqi.plus.external.config.pay.dto.*;
import com.juzishuke.framework.common.response.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 新支付系统
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/2 16:27
 */
@Component
@Slf4j
public class PayExternal extends DoCallService {

    /**
     * 提交支付（划扣）
     */
    public BaseResponse<ConfirmPayResp> confirmPay(ConfirmPayReq req) {
        return super.withCall(new AbstractCallContent<ConfirmPayResp>("/calm-pay/repay/confirmPay",req) {
            @Override
            public BaseResponse<ConfirmPayResp> deserialize(String result){
                return JSON.parseObject(result,new TypeReference<BaseResponse<ConfirmPayResp>>(){});
            }
        });
    }

    /**
     * 查询用户绑卡列表
     */
    public BaseResponse<UserCardListResp> getUserCardList(UserCardListReq req) {
        return super.withCall(new AbstractCallContent<UserCardListResp>("/calm-pay/bankCard/userCardList",req) {
            @Override
            public BaseResponse<UserCardListResp> deserialize(String result) {
                return JSON.parseObject(result,new TypeReference<BaseResponse<UserCardListResp>>(){});
            }
        });
    }

    /**
     * 纯代付
     */
    public BaseResponse<DefrayPayResp> defrayPay(DefrayPayReq req) {
        return super.withCall(new AbstractCallContent<DefrayPayResp>("/calm-pay/payment/defrayPay",req) {
            @Override
            public BaseResponse<DefrayPayResp> deserialize(String result){
                return JSON.parseObject(result,new TypeReference<BaseResponse<DefrayPayResp>>(){});
            }
        });
    }


    /**
     * 查询支付状态
     */
    public BaseResponse<TradeQueryResponse> tradeQuery(String thirdPayNum) {
        TradeQueryRequest tradeQueryRequest = new TradeQueryRequest();
        tradeQueryRequest.setThirdPayNum(thirdPayNum);
        return super.withCall(new AbstractCallContent<TradeQueryResponse>("/calm-pay/repay/tradeQuery",tradeQueryRequest) {
            @Override
            public BaseResponse<TradeQueryResponse> deserialize(String result){
                return JSON.parseObject(result,new TypeReference<BaseResponse<TradeQueryResponse>>(){});
            }
        });
    }

    /**
     * 收银台支付结果查询
     */
    public BaseResponse<CashierTradeQueryResponse> cashierTradeQuery(String thirdPayNum) {
        TradeQueryRequest tradeQueryRequest = new TradeQueryRequest();
        tradeQueryRequest.setThirdPayNum(thirdPayNum);
        return super.withCall(new AbstractCallContent<CashierTradeQueryResponse>("/calm-cashier/cashier/tradeQuery",tradeQueryRequest) {
            @Override
            public BaseResponse<CashierTradeQueryResponse> deserialize(String result){
                return JSON.parseObject(result,new TypeReference<BaseResponse<CashierTradeQueryResponse>>(){});
            }
        });
    }


    /**
     * 查询代付结果
     */
    public BaseResponse<DefrayPayResultResp> queryDefrayResult(QueryDefrayReq queryDefrayReq) {
        return super.withCall(new AbstractCallContent<DefrayPayResultResp>("/calm-pay/payment/queryDefray",queryDefrayReq) {
            @Override
            public BaseResponse<DefrayPayResultResp> deserialize(String result){
                return JSON.parseObject(result,new TypeReference<BaseResponse<DefrayPayResultResp>>(){});
            }
        });
    }

    /**
     * 查询退款结果
     */
    public BaseResponse<QueryRefundResultResp> queryRefundTrade(RefundQueryReq refundQueryReq) {
        return super.withCall(new AbstractCallContent<QueryRefundResultResp>("/calm-pay/refund/queryRefundTrade",refundQueryReq) {
            @Override
            public BaseResponse<QueryRefundResultResp> deserialize(String result){
                return JSON.parseObject(result,new TypeReference<BaseResponse<QueryRefundResultResp>>(){});
            }
        });
    }

    /**
     * 商户余额查询
     */
    public BaseResponse<ChannelMerBalanceResp> getChannelMerBalance(ChannelMerBalanceReq req) {
        return super.withCall(new AbstractCallContent<ChannelMerBalanceResp>("/calm-pay/balance/getChannelMerBalance",req) {
            @Override
            public BaseResponse<ChannelMerBalanceResp> deserialize(String result) {
                return JSON.parseObject(result,new TypeReference<BaseResponse<ChannelMerBalanceResp>>(){});
            }
        });
    }
}

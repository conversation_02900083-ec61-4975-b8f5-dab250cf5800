package com.juzifenqi.plus.external;

import com.juzifenqi.support.SupportResult;
import com.juzifenqi.support.api.MicroPayApi;
import com.juzifenqi.support.bean.dto.microPay.DefrayReqDto;
import com.juzifenqi.support.bean.dto.microPay.DefrayRespDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 支持support
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/13 11:18
 */
@Component
@Slf4j
public class SupportExternal {

    @Autowired
    private MicroPayApi microPayApi;

    /**
     * 订单micro代付
     */
    public SupportResult<DefrayRespDto> defrayPay(DefrayReqDto reqDto) {
        return microPayApi.defrayPay(reqDto);
    }
}

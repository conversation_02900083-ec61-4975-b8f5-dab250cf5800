package com.juzifenqi.plus.external;

import com.juzifenqi.magic.api.PlusCheckApi;
import com.juzifenqi.magic.api.PlusMarketApi;
import com.juzifenqi.magic.bean.PlusMagicResult;
import com.juzifenqi.magic.bean.dto.MarketDTO;
import com.juzifenqi.magic.bean.dto.OpenCheckDTO;
import com.juzifenqi.magic.bean.vo.PlusMarketDetailVO;
import com.juzifenqi.magic.bean.vo.PlusOpenDetailVO;
import com.juzifenqi.plus.enums.PreOpenlEnum;
import java.util.UUID;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 会员营销
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/22 18:23
 */
@Component
public class MagicExternal {

    @Autowired
    private PlusMarketApi plusMarketApi;
    @Autowired
    private PlusCheck<PERSON>pi  checkApi;

    /**
     * 按业务场景营销
     */
    public PlusMagicResult<PlusMarketDetailVO> marketForScene(MarketDTO dto) {
        return plusMarketApi.marketForScene(dto);
    }


    /**
     * 开通前校验
     */
    public PlusMagicResult<PlusOpenDetailVO> preOpenCheck(OpenCheckDTO checkDTO) {
        checkDTO.setRequestId(UUID.randomUUID().toString());
        String chainCode = PreOpenlEnum.getOpenChainCodeById(checkDTO.getChannel());
        if (StringUtils.isBlank(chainCode)) {
            throw new RuntimeException("未配置开通流程编排code，请联系客服配置后重试");
        }
        checkDTO.setChainCode(chainCode);
        return checkApi.preOpenCheck(checkDTO);
    }
}

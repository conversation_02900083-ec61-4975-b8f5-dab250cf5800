package com.juzifenqi.plus.external;

import com.juzifenqi.auth.JuziAuthResult;
import com.juzifenqi.auth.api.JuziAuthQueryApi;
import com.juzifenqi.auth.v3.JuziAuthQueryDetailApi;
import com.jzfq.auth.core.api.entiy.AuthBasicDetail;
import com.jzfq.auth.core.api.entiy.AuthIdentityDetail;
import com.jzfq.auth.core.api.entiy.AuthResult;
import com.jzfq.auth.core.entity.AuthApproval;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 认证
 *
 * <AUTHOR>
 * @date 2023/9/14 11:25
 **/
@Component
public class AuthExternal {

    @Autowired
    private JuziAuthQueryDetailApi juziAuthQueryDetailApi;
    @Resource
    private JuziAuthQueryApi       juziAuthQueryApi;

    public JuziAuthResult<AuthIdentityDetail> getAuthIdentityDetail(AuthResult authResult) {
        return juziAuthQueryDetailApi.getAuthIdentityDetail(authResult, true);
    }

    public JuziAuthResult<AuthBasicDetail> getAuthBasicDetail(AuthResult authResult) {
        return juziAuthQueryDetailApi.getAuthBasicDetail(authResult);
    }

    public JuziAuthResult<AuthApproval> getAuthState(AuthApproval authApproval){
        return juziAuthQueryApi.getUserAuthStatus(authApproval);
    }
}

package com.juzifenqi.plus.external;

import com.juzifenqi.virtual.api.admin.VirtualGoodsApi;
import com.juzifenqi.virtual.api.order.VirtualOrdersApi;
import com.juzifenqi.virtual.bean.bo.VirtualOrdersBo;
import com.juzifenqi.virtual.bean.pojo.VirtualGoods;
import com.juzifenqi.virtual.bean.pojo.VirtualOrders;
import com.juzifenqi.virtual.bean.system.VirtualResult;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 虚拟权益服务
 *
 * <AUTHOR>
 * @date 2023/9/14 11:11
 **/
@Component
@Slf4j
public class VirtualExternal {

    @Autowired
    private VirtualOrdersApi virtualOrdersApi;
    @Autowired
    private VirtualGoodsApi  virtualGoodsApi;

    public VirtualResult<List<VirtualOrders>> countOrderByPlus(String plusOrderSn,
            Integer modelId) {
        return virtualOrdersApi.countOrderByPlus(plusOrderSn, modelId);
    }

    public VirtualResult<List<VirtualGoods>> getVirtualGoodsBySkus(String skus) {
        return virtualGoodsApi.getVirtualGoodsBySkus(skus);
    }

    /**
     * 获取充值中订单列表
     *
     * @param plusOrderSn 开通会员单号
     */
    public VirtualResult<List<VirtualOrders>> getProcessingOrder(String plusOrderSn,
            Integer modelId) {
        return virtualOrdersApi.selectProcessingOrder(plusOrderSn, modelId);
    }

    /**
     * 提交订单-无需支付
     */
    public VirtualResult createVirtualOrder(VirtualOrders virtualOrders) {
        return virtualOrdersApi.submitOrder(virtualOrders);
    }

    /**
     * 提交订单-需要支付
     */
    public VirtualResult<Boolean> createVirtualOrderForPay(VirtualOrders virtualOrders) {
        return virtualOrdersApi.ordersSubmit(virtualOrders);
    }

    /**
     * 取消会员虚拟权益订单
     */
    public VirtualResult cancelVirtualOrder(String plusOrderSn) {
        return virtualOrdersApi.cancelOrdersByPlus(plusOrderSn);
    }

    /**
     * 获取商城下单已完成的订单
     */
    public VirtualResult<List<VirtualOrders>> getSettlementOrderList(String plusOrderSn,
            Integer memberId, Integer modelId) {
        VirtualOrdersBo virtualOrdersBo = new VirtualOrdersBo();
        virtualOrdersBo.setPlusOrderSn(plusOrderSn);
        virtualOrdersBo.setUserId(memberId);
        virtualOrdersBo.setModelId(modelId);
        return virtualOrdersApi.getSettlementOrderList(virtualOrdersBo);
    }

    /**
     * 获取商城下单全部的订单
     */
    public VirtualResult<List<VirtualOrders>> getAllOrderList(String plusOrderSn, Integer memberId,
            Integer modelId) {
        VirtualOrdersBo virtualOrdersBo = new VirtualOrdersBo();
        virtualOrdersBo.setPlusOrderSn(plusOrderSn);
        virtualOrdersBo.setUserId(memberId);
        virtualOrdersBo.setModelId(modelId);
        return virtualOrdersApi.getAllOrderList(virtualOrdersBo);
    }

}

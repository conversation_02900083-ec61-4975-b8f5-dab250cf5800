package com.juzifenqi.plus.external.config;//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//


import com.juzishuke.framework.common.response.BaseResponse;

public abstract class AbstractCallContent<T> {
    private final String resourceUrl;
    private final Object param;

    public AbstractCallContent(String resourceUrl, Object param) {
        this.resourceUrl = resourceUrl;
        this.param = param;
    }

    public Object getParam() {
        return this.param;
    }

    public String getResourceUrl() {
        return this.resourceUrl;
    }

    public abstract BaseResponse<T> deserialize(String var1) throws Exception;
}

package com.juzifenqi.plus.external;

import com.jzsk.rms.vps.api.RpcVariableService;
import com.jzsk.rms.vps.dto.domain.ResponseVo;
import com.jzsk.rms.vps.dto.domain.VpsReqParamsDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 风控
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/5 17:19
 */
@Component
public class RmsExternal {

    @Autowired
    private RpcVariableService variableService;

    /**
     * 获取风控变量
     */
    public ResponseVo getVariable(VpsReqParamsDto dto) throws Exception {
        return variableService.queryVariable(dto);
    }

}

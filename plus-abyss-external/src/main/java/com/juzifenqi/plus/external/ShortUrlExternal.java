package com.juzifenqi.plus.external;

import com.alibaba.fastjson.JSONObject;
import com.groot.utils.http.OKHttp3Utils;
import com.groot.utils.http.OkHttpClientEnum;
import com.juzifenqi.plus.config.ConfigProperties;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 短链
 *
 * <AUTHOR>
 * @date 2024/5/24 下午5:05
 */
@Slf4j
@Component
public class ShortUrlExternal {

    @Autowired
    private ConfigProperties configProperties;

    /**
     * 请求路径
     */
    private static final String PATH = "/shortLink/manager/getBatchShortLinkConfig";

    /**
     * 生成短链
     */
    public JSONObject generateShortUrl(String longUrl) {
        long start = System.currentTimeMillis();
        // 构建请求参数
        Map<String, Object> param = buildSendParam(longUrl);
        // 请求地址
        String sendUrl = configProperties.shortUrl + PATH;
        String sendParam = JSONObject.toJSONString(param);
        log.info("请求生成短链,请求地址:{},参数:{}", sendUrl, sendParam);
        // 短链服务预估10ms
        JSONObject result = OKHttp3Utils.postByJson(sendUrl, null, sendParam,
                OkHttpClientEnum.HALF_SECOND, 0);
        long end = System.currentTimeMillis();
        log.info("请求生成短链结束,耗时:{}ms,返回结果:{}", end - start, result);
        return result;
    }

    /**
     * 构建请求参数
     */
    private Map<String, Object> buildSendParam(String longUrl) {
        Map<String, Object> value = new HashMap<>();
        // 转换的长链接
        value.put("longLink", longUrl);
        // 域名类型
        value.put("domainType", configProperties.shortUrlDomainType);
        // 链接类型:一人一链
        value.put("linkType", 1);
        // 秘钥
        value.put("accessKey", configProperties.shortUrlAccessKey);
        // 有效期一个月
        value.put("dateType", 5);
        // 备注
        value.put("remark", "权益0元发放");
        Map<String, Object> param = new HashMap<>();
        param.put(longUrl, value);
        return param;
    }

}

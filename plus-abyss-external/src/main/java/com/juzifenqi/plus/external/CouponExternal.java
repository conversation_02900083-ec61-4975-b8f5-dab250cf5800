package com.juzifenqi.plus.external;

import com.juzifenqi.coupon.api.common.CouponQueryApi;
import com.juzifenqi.coupon.api.common.CouponUserOptApi;
import com.juzifenqi.coupon.api.common.CouponUserQueryApi;
import com.juzifenqi.coupon.api.common.v3.CouponUserQueryApiV3;
import com.juzifenqi.coupon.entity.common.CouponResult;
import com.juzifenqi.coupon.entity.enums.CouponSourceEnum;
import com.juzifenqi.coupon.entity.shop.JuziCoupon;
import com.juzifenqi.coupon.entity.shop.JuziCouponUser;
import com.juzifenqi.coupon.entity.shop.vo.CouponViewVo;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 优惠券
 *
 * <AUTHOR>
 * @date 2023/9/14 11:34
 **/
@Component
public class CouponExternal {

    @Autowired
    private CouponQueryApi       couponQueryApi;
    @Autowired
    private CouponUserOptApi     couponUserOptApi;
    @Autowired
    private CouponUserQueryApi   couponUserQueryApi;
    @Autowired
    private CouponUserQueryApiV3 couponUserQueryApiV3;

    public CouponResult<JuziCoupon> getCouponById(Integer couponId) {
        return couponQueryApi.getCouponById(couponId);
    }

    public CouponResult<Map<String, List<JuziCouponUser>>> batchSendCouponBackUser(
            List<JuziCouponUser> couponUsers) {
        return couponUserOptApi.batchSendCouponBackUser(couponUsers, true);
    }

    public CouponResult<List<CouponViewVo>> getCouponById(Integer memberId, Integer channel,
            List<Integer> couponIds) {
        return couponUserQueryApi.getUserCoupons(memberId, channel, couponIds);
    }

    public CouponResult<List<CouponViewVo>> getCouponUserByIds(Integer memberId,
            List<Integer> couponUserIdList) {
        return couponUserQueryApi.getCouponUserByIds(memberId, couponUserIdList);
    }

    public CouponResult<Integer> receiveCouponForMemberPlus(Integer couponId, Integer memberId) {
        return couponUserOptApi.receiveCouponForMemberPlus(couponId, memberId, null);
    }

    public CouponResult<JuziCouponUser> getCouponUserById(Integer couponUserId) {
        return couponUserQueryApi.getCouponUserById(couponUserId);
    }

    public CouponResult<List<CouponViewVo>> getUserCoupons(Integer memberId, Integer channel,
            List<Integer> couponIds) {
        return couponUserQueryApi.getUserCoupons(memberId, channel, couponIds);
    }

    /**
     * 批量删除用户优惠券
     */
    public CouponResult<Integer> batchDelById(List<Integer> couponIds, Integer memberId) {
        return couponUserOptApi.batchDelByCouponUserId(couponIds, "plus-abyss", memberId);
    }

    /**
     * 根据id或者还款券流水号取得用户优惠券信息
     */
    public CouponResult<JuziCouponUser> getCouponUserByIdOrCouponNo(Integer couponUserId,
            String couponNo, CouponSourceEnum couponSourceEnum) {
        return couponUserQueryApiV3.getCouponUserByIdOrCouponNo(couponUserId, couponNo,
                couponSourceEnum);
    }

    /**
     * 根据couponIdList获取优惠券信息
     */
    public CouponResult<List<JuziCoupon>> getCouponByListId(List<Integer> couponIdList) {
        return couponQueryApi.getCouponsByIdList(couponIdList);
    }

    /**
     * 根据优惠券ID、用户ID和发送状态获取优惠券
     */
    public CouponResult<Map<String, Object>> getCouponByIdsAndSendStatus(List<Integer> couponIdList,
            int sendStatus, Integer userId) {
        return couponQueryApi.getCouponByIdsAndSendStatus(couponIdList, sendStatus, userId);
    }

    /**
     * 获取领取的还款优惠券
     */
    public CouponResult<List<CouponViewVo>> getUserRepayCoupons(Integer memberId,
            String couponNos) {
        return couponUserQueryApiV3.getCouponRepayResp(memberId, couponNos, CouponSourceEnum.plus);
    }
}

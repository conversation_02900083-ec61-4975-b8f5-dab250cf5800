package com.juzifenqi.plus.external;

import com.juzifenqi.aplha.api.PlusCancelCheckApi;
import com.juzifenqi.aplha.bean.PlusAplhaResult;
import com.juzifenqi.aplha.bean.dto.CancelCheckDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 阿尔法校验
 *
 * <AUTHOR>
 * @date 2023/9/14 11:11
 **/
@Component
public class AlphaExternal {

    @Autowired
    private PlusCancelCheckApi plusCancelCheckApi;

    public PlusAplhaResult validate(CancelCheckDTO dto) {
        return plusCancelCheckApi.validate(dto);
    }

}

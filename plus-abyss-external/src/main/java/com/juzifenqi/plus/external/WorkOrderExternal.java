package com.juzifenqi.plus.external;

import com.juzifenqi.work.api.ApiResult;
import com.juzifenqi.work.api.auth.AuthInfoApi;
import com.juzifenqi.work.bean.workbench.param.UserAuthParam;
import com.juzifenqi.work.bean.workbench.vo.UserAuthRecordVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 工单
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/9 10:33
 */
@Component
public class WorkOrderExternal {

    @Autowired
    private AuthInfoApi authInfoApi;

    /**
     * 查询识别场景=客服的最新一次人脸识别结果
     * <p>此接口只需要传userId，只会返回场景=客服的数据</p>
     */
    public ApiResult<UserAuthRecordVo> queryUserLastAuthInfo(UserAuthParam authParam) {
        return authInfoApi.queryUserLastAuthInfo(authParam);
    }
}

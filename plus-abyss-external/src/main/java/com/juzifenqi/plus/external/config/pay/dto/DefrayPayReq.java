package com.juzifenqi.plus.external.config.pay.dto;

import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/10/30
 */
@Data
public class DefrayPayReq {

    private BigDecimal tradeAmount;
    private String     tradeSubject;
    private String     notifyUrl;
    private String     orderId;
    private String     thirdPayNum;
    private String     application;
    private String     businessScene;
    private String     payProductCode;
    private String     channelMerchant;
    private String     payeeCardType;
    private String     payeeAccountNo;
    private String     payeeAccountName;
    private String     payeeAccountType;
    private String     payeeBankAssociatedCode;
    private String     transCardId;
    private String     payeeBankName;
    private String     defrayPayType;
    private String     payeeBankProvince;
    private String     payeeBankCity;
    private String     source;
    private String     version;
}

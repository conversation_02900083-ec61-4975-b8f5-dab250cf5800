package com.juzifenqi.plus.external;

import com.juzifenqi.member.entity.member.MemberInfo;
import com.juzifenqi.onapi.admin.store.IStoreInfoService;
import com.juzifenqi.onshop.bean.result.RestResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 分销
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/18 14:20
 */
@Component
public class OnlineExternal {

    @Autowired
    private IStoreInfoService storeInfoService;

    /**
     * 分销开店
     */
    public RestResponse<Boolean> openStore(MemberInfo member, String remark) {
        RestResponse<Integer> resp = storeInfoService.openStoreSilence(member,
                remark);
        boolean result = resp != null && resp.getData() != null;
        return new RestResponse<>(result);
    }
}

package com.juzifenqi.plus.external;

import com.juzifenqi.act.api.SignInSystemApi;
import com.juzifenqi.act.bean.enums.SignInTypeEnum;
import com.juzifenqi.act.rpc.result.ActivityResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 活动
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/6 16:54
 */
@Component
public class ActivityExternal {

    @Autowired
    private SignInSystemApi signInSystemApi;

    /**
     * 发放桔豆
     */
    public ActivityResult<Boolean> sendBean(Integer userId, Integer beanNum,
            SignInTypeEnum typeEnum) {
        return signInSystemApi.sendBean(userId, beanNum, typeEnum);
    }
}

package com.juzifenqi.plus.external;

import com.juzifenqi.product.dto.PagerInfo;
import com.juzifenqi.product.dto.ProductServiceResult;
import com.juzifenqi.product.entity.MemberPlusGoodsVo;
import com.juzifenqi.product.entity.Product;
import com.juzifenqi.product.entity.ProductGoods;
import com.juzifenqi.product.entity.ProductQueryParamVo;
import com.juzifenqi.product.search.service.IProductRedisService;
import com.juzifenqi.product.search.vo.ProductDetailCacheVo;
import com.juzifenqi.product.search.vo.ProductSearchResult;
import com.juzifenqi.product.service.IProductGoodsService;
import com.juzifenqi.product.service.IProductService;
import com.juzifenqi.product.service.IStagesService;
import com.juzifenqi.virtual.api.admin.VirtualGoodsApi;
import com.juzifenqi.virtual.bean.pojo.VirtualGoods;
import com.juzifenqi.virtual.bean.system.VirtualResult;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 商品
 *
 * <AUTHOR>
 * @date 2023/9/14 11:16
 **/
@Component
public class ProductExternal {

    @Autowired
    private IProductService      productService;
    @Autowired
    private IProductRedisService productRedisService;
    @Autowired
    private IProductGoodsService productGoodsService;
    @Resource
    private IStagesService       stagesService;
    @Resource
    private VirtualGoodsApi      virtualGoodsApi;


    public ProductServiceResult<Product> getById(Integer productId) {
        return productService.getById(productId);
    }

    public ProductServiceResult<List<Product>> getByIds(List<Integer> productIds) {
        return productService.getProductsByIds(productIds);
    }

    public ProductServiceResult<ProductGoods> getProductGoodsBySku(String sku) {
        return productGoodsService.getProductGoodsBySku(sku);
    }

    public ProductServiceResult<List<ProductGoods>> getProductGoodsListByList(
            List<String> skuList) {
        return productGoodsService.getProductGoodsListByList(skuList);
    }

    public ProductSearchResult<ProductDetailCacheVo> getCacheById(Integer productId) {
        return productRedisService.getProductDetailById(productId);
    }

    public ProductServiceResult<Integer> updateMemberPlusGoods(
            MemberPlusGoodsVo memberPlusGoodsVo) {
        return productGoodsService.updateMemberPlusGoods(memberPlusGoodsVo);
    }

    public ProductServiceResult<Integer> createMemberPlusGoods(
            MemberPlusGoodsVo memberPlusGoodsVo) {
        return productGoodsService.createMemberPlusGoods(memberPlusGoodsVo);
    }

    public ProductServiceResult<List<Product>> pageProductList(ProductQueryParamVo vo,
            PagerInfo pagerInfo) {
        return productService.pageProductList(vo, pagerInfo);
    }

    public ProductServiceResult<String> tagFreeByProductId(Integer productId) {
        return stagesService.tagFreeByProductId(productId);
    }

    public ProductServiceResult<String> getMaxPeriodInformation(Integer productId, Integer isSelf) {
        return stagesService.getMaxPeriodInformation(productId, isSelf);
    }

    public ProductServiceResult<Integer> updateMemberPlusGoodsState(String id, Integer type) {
        return productGoodsService.updateMemberPlusGoodsState(Integer.valueOf(id), type);
    }

    public VirtualResult<List<VirtualGoods>> getVirtualGoodsBySkus(String skus) {
        return virtualGoodsApi.getVirtualGoodsBySkus(skus);
    }

    public ProductSearchResult<ProductDetailCacheVo> getProductDetailById(Integer productId){
        return productRedisService.getProductDetailById(productId);
    }
}
package com.juzifenqi.plus.external.config.pay.dto;

import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/10/30
 */

@Data
public class QueryRefundResultResp {

    private String     status;
    private BigDecimal refundAmount;
    private String     payChannel;
    private String     applyTime;
    private String     successTime;
    private String     errorCode;
    private String     errorMsg;
    private String     ctCardNo;
    private String     serialNumber;
    private String     refundType;
    private String     customerName;
    private String     bankCardName;
}

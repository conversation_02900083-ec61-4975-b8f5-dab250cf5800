package com.juzifenqi.plus.external;

import com.alibaba.fastjson.JSONObject;
import com.juzishuke.framework.common.response.BaseResponse;
import com.yikoudai.zijin.cove.open.PlanApi;
import com.yikoudai.zijin.cove.open.bill.req.PlanQueryReq;
import com.yikoudai.zijin.cove.open.bill.req.PlanSummaryReq;
import com.yikoudai.zijin.cove.open.bill.resp.PlanQueryResp;
import com.yikoudai.zijin.cove.open.bill.resp.PlanSummaryResp;
import com.yikoudai.zijin.cove.open.info.RepayPlanInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;


@Component
@Slf4j
public class AcmExternal {

    @Resource
    private PlanApi planApi;

//    /**
//     * 失效还款券
//     */
//    public JsonResult<List<String>> disableCoupon(String couponNo) {
//        CouponRepayDisableReq cdr = new CouponRepayDisableReq();
//        cdr.setCouponNo(couponNo);
//        return repayRemoteService.disableCoupon(cdr);
//    }

    /**
     * 拉取还款计划
     */
    public List<RepayPlanInfo> getRepayPlan(PlanQueryReq req) {
        log.info("查询资金还款计划入参：{}", JSONObject.toJSONString(req));
        BaseResponse<PlanQueryResp> repayPlans = planApi.getRepayPlans(req);
        log.info("查询核心还款计划接口返回:{}", JSONObject.toJSONString(repayPlans));
        if (repayPlans == null || repayPlans.getData() == null) {
            return null;
        }
        if (CollectionUtils.isEmpty(repayPlans.getData().getOrderInfos())) {
            return null;
        }
        return repayPlans.getData().getOrderInfos().get(0).getRepayPlanInfos();
    }

    public PlanSummaryResp queryRepayPlanSummary(PlanSummaryReq planSummaryReq) {
        log.info("新帐单-查询还款计划统计接口-参数[{}]", JSONObject.toJSONString(planSummaryReq));
        BaseResponse<PlanSummaryResp> baseResponse = planApi.queryRepayPlanSummary(planSummaryReq);
        log.info("新帐单-查询还款计划统计接口-返回结果:[{}]", JSONObject.toJSONString(baseResponse));
        if (baseResponse == null || !baseResponse.isSuccess() || baseResponse.getData() == null) {
            throw new RuntimeException("用户账单查询异常");
        }
        return baseResponse.getData();
    }
}

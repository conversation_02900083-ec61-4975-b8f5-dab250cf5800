package com.juzifenqi.plus.external.config;//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//


import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.juzifenqi.plus.external.config.pay.dto.OpenApiReply;
import com.juzishuke.framework.common.response.BaseResponse;
import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CancellationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;

public class DoCallService {
    protected final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    CalmOpenApiProperties calmOpenApiProperties;
    protected static ObjectMapper mapper;

    public DoCallService() {
    }

    protected <T> BaseResponse<T> withCall(AbstractCallContent<T> callContent) {
        Object param = callContent.getParam();
        String jsonParam = null;
        if (!ObjectUtils.isEmpty(param)) {
            try {
                jsonParam = mapper.writeValueAsString(param);
            } catch (JsonProcessingException var12) {
                JsonProcessingException e = var12;
                this.logger.error("请求 calm 服务参数转换 json 字符串失败", e);
                return BaseResponse.fail(OpenApiReply.OPEN_REQ_DATA_FAIL);
            }
        }

        Map<String, String> headers = new HashMap(2);
        headers.put("appId", this.calmOpenApiProperties.getAppId());
        String result = "";

        try {
            String url = callContent.getResourceUrl();
            this.logger.info("调用calm服务地址路径[{}], 请求参数[{}]", url, jsonParam);
            result = OkHttpUtil.postJson(this.calmOpenApiProperties.getServerAddr() + url, headers, jsonParam);
            this.logger.info("调用calm服务响应结果[{}]", result);
            return ObjectUtils.isEmpty(result) ? BaseResponse.fail(OpenApiReply.OPEN_NO_RESULT) : callContent.deserialize(result);
        } catch (JsonProcessingException var7) {
            JsonProcessingException e = var7;
            this.logger.error("请求calm服务过程中发生：参数解析失败异常,原始响应数据[{}]", result, e);
            return BaseResponse.fail(OpenApiReply.OPEN_RES_DATA_FAIL);
        } catch (CancellationException var8) {
            CancellationException e = var8;
            this.logger.error("请求calm服务过程中发生：CancellationException异常", e);
            return BaseResponse.fail(OpenApiReply.OPEN_INACTIVITY_CANCEL);
        } catch (SocketTimeoutException var9) {
            SocketTimeoutException e = var9;
            this.logger.error("请求calm服务过程中发生：SocketTimeoutException异常", e);
            return BaseResponse.fail(OpenApiReply.OPEN_SOCKET_TIME_OUT);
        } catch (ConnectException var10) {
            ConnectException e = var10;
            this.logger.error("请求calm服务过程中发生：ConnectException异常", e);
            return BaseResponse.fail(OpenApiReply.OPEN_CONNECT_FAIL);
        } catch (Exception var11) {
            Exception e = var11;
            this.logger.error("请求calm服务过程中发生：未知异常", e);
            return BaseResponse.fail(OpenApiReply.OPEN_UNKNOWN_ERROR);
        }
    }

    static {
        mapper = (new ObjectMapper()).configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false).setSerializationInclusion(Include.NON_NULL);
    }
}

package com.juzifenqi.plus.external;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.groot.utils.http.OKHttp3SimpleUtils;
import com.juzifenqi.alita.result.BaseResult;
import com.juzifenqi.core.ServiceResult;
import com.juzifenqi.oms.api.OmsResult;
import com.juzifenqi.oms.api.order.OmsLoanOrderApi;
import com.juzifenqi.oms.bean.OmsLoanParams;
import com.juzifenqi.oms.bean.OrderBase;
import com.juzifenqi.order.dao.entity.Orders;
import com.juzifenqi.order.dao.entity.OrdersBase;
import com.juzifenqi.order.dao.entity.OrdersResubmitRecord;
import com.juzifenqi.order.dto.OrderQueryDTO;
import com.juzifenqi.order.dto.OrderSimpleInfoDTO;
import com.juzifenqi.order.service.IOrderQueryApi;
import com.juzifenqi.order.service.IOrderService;
import com.juzifenqi.order.vo.OrderCancelRefundResultVO;
import com.juzifenqi.order.vo.OrderCancelRefundVO;
import com.juzifenqi.order.vo.OrderExtraVO;
import com.juzifenqi.order.vo.OrderQueryVO;
import com.juzifenqi.order.vo.OrderUpdateVO;
import com.juzifenqi.order.vo.OrdersCancelVO;
import com.juzifenqi.order.vo.product.OrderProductVo;
import com.juzifenqi.plus.constants.CommonConstant;
import com.juzifenqi.service.IOrdersResubmitRecordService;
import com.juzifenqi.service.OldBeanIOrdersBaseService;
import com.juzifenqi.service.OldBeanIOrdersService;
import com.juzifenqi.service.OldBeanJzfqIOrdersOtherService;
import com.juzifenqi.service.OldBeanJzfqIPaidMemberorderService;
import com.juzifenqi.vo.MemberVipVO;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 订单
 *
 * <AUTHOR>
 * @date 2023/9/14 10:46
 **/
@Component
@Slf4j
public class OrderExternal {

    @Autowired
    private OldBeanJzfqIPaidMemberorderService oldBeanJzfqIPaidMemberorderService;
    @Autowired
    private IOrderQueryApi                     orderQueryApi;
    @Autowired
    private OmsLoanOrderApi                    omsLoanOrderApi;
    @Autowired
    private OldBeanJzfqIOrdersOtherService     oldBeanJzfqIOrdersOtherService;
    @Autowired
    private IOrderService                      iOrderService;
    @Autowired
    private OldBeanIOrdersBaseService          oldBeanIOrdersBaseService;
    @Autowired
    private OldBeanIOrdersService              oldBeanIOrdersService;
    @Resource
    private IOrdersResubmitRecordService       ordersResubmitRecordService;

    @Value("${order.url:https://ins-jdd-flow-service.jdd.com}")
    private String orderUrl;

    /**
     * 会员订单提交
     */
    public ServiceResult<Map<String, String>> emberOrderCommit(MemberVipVO memberVipVO) {
        return oldBeanJzfqIPaidMemberorderService.emberOrderCommit(memberVipVO, false);
    }

    /**
     * 取消会员订单-不退款
     */
    public ServiceResult<Boolean> closePlusOrder(String orderSn) {
        return orderQueryApi.closePlusOrder(orderSn);
    }

    /**
     * 获取订单信息
     */
    public ServiceResult<OrderSimpleInfoDTO> getCommonDataBySn(String orderSn) {
        return orderQueryApi.getCommonDataBySn(orderSn);
    }

    /**
     * 获取base订单信息
     */
    public ServiceResult<OrdersBase> getBaseCommonDataBySn(String orderSn) {
        return oldBeanIOrdersBaseService.getCommonDataBySn(orderSn);
    }

    /**
     * 获取订单+扩展信息
     */
    public ServiceResult<OrderExtraVO> getCommonDataAndExtraBySn(String orderSn) {
        return oldBeanIOrdersBaseService.getCommonDataAndExtraBySn(orderSn);
    }

    /**
     * 取消会员订单-退全款
     */
    public Boolean cancelOrderForCusys(String orderSn, String reason, String operatorName) {
        return oldBeanJzfqIOrdersOtherService.cancelOrderForCusys(orderSn, reason, operatorName)
                .getResult();
    }

    /**
     * 虚拟订单提交
     */
    public ServiceResult<Map<String, String>> virtualOrderCommit(MemberVipVO memberVipVO) {
        return oldBeanJzfqIPaidMemberorderService.commitVirtualOrder(memberVipVO);
    }

    /**
     * 部分退款取消订单 orderSn, reason, operatingName, operatingId,  moneyBack
     */
    public Boolean cancelOrderPartRefund(String orderSn, String reason, String operatorName,
            Integer operatingId, BigDecimal moneyBack) {
        OrdersCancelVO cancelVO = new OrdersCancelVO();
        cancelVO.setCancelReason(reason);
        cancelVO.setOrderSn(orderSn);
        cancelVO.setMoneyBack(moneyBack);
        cancelVO.setOperatingId(operatingId);
        cancelVO.setOperatingName(operatorName);
        return oldBeanJzfqIOrdersOtherService.cancelOrderPartRefund(cancelVO).getResult();
    }

    /**
     * 客服操作修改订单状态
     */
    public BaseResult<Boolean> customerUpdateOrderState(String orderSn, Integer orderState,
            String reason, BigDecimal refundAmount, Integer optId, String optName) {
        OrderUpdateVO vo = new OrderUpdateVO();
        vo.setOrderSn(orderSn);
        vo.setOperatingName(optName);
        vo.setOperatingId(optId);
        // 1=支付成功 2=取消订单
        vo.setOperatingType(orderState == 2 ? 1 : 2);
        vo.setUpdateReason(reason);
        vo.setMoneyBack(refundAmount);
        return iOrderService.plusUpdateOrderState(vo);
    }

    /**
     * 撞库校验
     */
    public Boolean checkStrikeBase(Integer userId) {
        Integer SUCCESS = 200;
        String url = orderUrl + "/jdd-flow/qualify";
        log.info("请求订单 撞库 userId:{},url:{}", userId, url);
        Map<String, String> headMap = Maps.newHashMap();
        headMap.put("userId", String.valueOf(userId));
        Map<String, Object> parameter = new HashMap<>();
        JSONObject result = OKHttp3SimpleUtils.postByJson(url, headMap,
                JSONObject.toJSONString(parameter));
        log.info("请求订单 撞库 返回 result:{}", JSONObject.toJSONString(result));
        if (result == null) {
            return false;
        }
        Integer responseCode = result.getInteger("responseCode");
        if (SUCCESS.equals(responseCode)) {
            JSONObject responseVal = result.getJSONObject("responseVal");
            if (!SUCCESS.equals(responseVal.getInteger("code"))) {
                log.info("调用订单撞库 异常 code：{},msg:{}", result.getInteger("code"),
                        result.getString("message"));
                return false;
            }
            Integer data = responseVal.getInteger("data");
            return data == 1;
        }
        log.info("请求订单 撞库失败 userId:{}", userId);
        return false;
    }

    /**
     * 失效订单
     */
    public ServiceResult<Integer> invalidOrder(List<String> orderSns) {
        return oldBeanIOrdersBaseService.cancelMemberAfterOrder(orderSns);
    }

    /**
     * 获取501或611借款单
     */
    public ServiceResult<Orders> lastOneProcessOrder(Integer userId) {
        return oldBeanIOrdersService.lastOneProcessOrder(userId);
    }

    /**
     * 关闭用户待支付的订单
     */
    public ServiceResult<Boolean> closeUnpayOrders(Integer userId, Integer channel) {
        return orderQueryApi.closeUserUnpayOrders(userId, channel, CommonConstant.PLUS_SYSTEM_FLAG);
    }

    /**
     * 更改订单利率
     */
    public BaseResult<Boolean> changeOrderRate(String orderSn, BigDecimal rate) {
        return iOrderService.changeOrderRate(orderSn, rate, CommonConstant.PLUS_SYSTEM_FLAG);
    }

    /**
     * 根据新订单号请求订单服务查询重提订单
     */
    public ServiceResult<OrdersResubmitRecord> getUserResubmitOrder(String newOrderSn) {
        return ordersResubmitRecordService.getByNewSn(newOrderSn);
    }

    /**
     * 获取用户所有的购买半价商品记录
     *
     * @param memberId 用户id
     * @param jxStartTime 开始时间
     * @param jxEndTime 结束时间
     * @param productIds 商品id集合
     */
    public ServiceResult<List<OrderProductVo>> getOrdersBaseByProductList(Integer memberId,
            Date jxStartTime, Date jxEndTime, List<Integer> productIds) {
        return oldBeanIOrdersBaseService.getOrdersBaseByProductList(memberId, jxStartTime,
                jxEndTime, productIds);
    }

    /**
     * 根据指定条件查询订单信息
     */
    public ServiceResult<List<OrdersBase>> getOrderByMemberId(OrderQueryVO queryVO) {
        return oldBeanIOrdersService.getOrderByMemberId(queryVO);
    }

    /**
     * 查询在途订单
     */
    public ServiceResult<List<OrderQueryDTO>> getOrderByMemberIdAndChannel(OrderQueryVO vo) {
        return orderQueryApi.getOrderByMemberIdAndChannel(vo);
    }

    /**
     * 重提订单创单接口
     */
    public OmsResult<OrderBase> orderSubmit(OmsLoanParams loanParams) {
        return omsLoanOrderApi.orderSubmit(loanParams);
    }

    /**
     * 查最近一笔611放款中的借款订单
     */
    public ServiceResult<Orders> lastOneLoanOrder(Integer memberId) {
        return oldBeanIOrdersService.lastOneLoanOrder(memberId);
    }

    /**
     * 获取订单信息（返回资方id）
     */
    public ServiceResult<Orders> getOrdersBySn(String orderSn) {
        return oldBeanIOrdersService.getOrdersBySn(orderSn);
    }

    /**
     * 调订单中心取消订单
     */
    public BaseResult<OrderCancelRefundResultVO> closeOrderRefund(OrderCancelRefundVO vo) {
        return iOrderService.closeOrderRefund(vo);
    }

}

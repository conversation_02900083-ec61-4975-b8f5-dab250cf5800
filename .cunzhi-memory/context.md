# 项目上下文信息

- 新增会员月卡续费计划功能模块，表名：plus_month_member_renewal_plan，包含批量插入、订单号查询、分页查询功能
- PlusOrderApplicationImpl.createPlusOrder方法是会员订单创建的核心入口，包含渠道校验、订单创建、会员开通等完整业务流程，使用事务保证数据一致性，采用策略模式处理不同会员类型
- supplierId字段来源：使用 PlusOrderShuntPo.planInSupplier 字段作为续费计划的收款主体ID
- 已完成会员月卡定时扣费功能实现：1.新增IPlusOrderRepository.getMonthCardOrdersForDeduction查询方法 2.修改PlusOrderApplicationImpl.deduct方法跳过会员月卡续费订单前置校验 3.新增PlusMonthMemberJob.plusMonthMemberDeduct定时任务方法

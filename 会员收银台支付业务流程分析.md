# 会员调用收银台完成支付的完整业务流程分析

## 1. 业务流程概述

会员调用收银台支付是会员系统与支付系统协作完成用户支付的核心流程。该流程涉及会员系统、订单中心、收银台、支付系统等多个组件的协调配合，通过标准化的接口和异步回调机制确保支付的可靠性和一致性。

## 2. 收银台调用流程分析

### 2.1 完整业务流程时序图

```plantuml
@startuml 会员收银台支付流程
participant "用户" as User
participant "前端" as Frontend
participant "会员系统" as MemberSystem
participant "订单中心" as OrderCenter
participant "收银台" as Cashier
participant "支付系统" as PaySystem
participant "第三方支付" as ThirdPay
participant "消息队列" as MQ

User -> Frontend: 发起支付请求
Frontend -> MemberSystem: 获取支付信息
MemberSystem -> MemberSystem: 校验订单状态
MemberSystem -> MemberSystem: 生成分账信息
MemberSystem -> MemberSystem: 构建支付参数
MemberSystem -> Frontend: 返回支付信息

Frontend -> OrderCenter: 提交支付请求
OrderCenter -> Cashier: 调用收银台接口
Cashier -> PaySystem: 发起支付
PaySystem -> ThirdPay: 调用第三方支付

ThirdPay -> PaySystem: 返回支付结果
PaySystem -> Cashier: 支付结果
Cashier -> OrderCenter: 支付响应

alt 同步支付成功
    OrderCenter -> Frontend: 返回支付成功
    Frontend -> User: 显示支付成功
else 同步支付失败
    OrderCenter -> Frontend: 返回支付失败
    Frontend -> User: 显示支付失败
end

note right: 异步回调处理
PaySystem -> MQ: 发送支付结果消息
MQ -> MemberSystem: 消费支付结果
MemberSystem -> MemberSystem: 处理支付回调
MemberSystem -> MemberSystem: 更新订单状态
MemberSystem -> MemberSystem: 发放会员权益
MemberSystem -> User: 发送开通通知
@enduml
```

### 2.2 系统组件交互关系

| 系统组件 | 职责 | 关键接口 |
|----------|------|----------|
| 会员系统 | 订单管理、支付信息生成 | `getOrderPayInfo()` |
| 订单中心 | 支付请求转发、状态同步 | 调用收银台接口 |
| 收银台 | 支付渠道选择、支付执行 | `/calm-cashier/cashier/*` |
| 支付系统 | 支付处理、结果回调 | `/calm-pay/repay/*` |
| 消息队列 | 异步消息传递 | 支付结果回调MQ |

## 3. 接口参数详细分析

### 3.1 获取支付信息接口

**接口定义**：
```java
PlusAbyssResult<PlusOrderPayInfoResp> getOrderPayInfo(String orderSn)
```

**请求参数**：
```java
public class PlusOrderPayInfoReq {
    private String orderSn;  // 会员订单号（必填）
}
```

**核心业务逻辑**：
```java
public PlusOrderPayInfoAo getOrderPayInfo(String orderSn) {
    // 1. 校验订单状态
    if (!Objects.equals(PlusOrderStateEnum.WAIT_PAY.getCode(), 
                       plusOrderEntity.getOrderState())) {
        throw new PlusAbyssException("请求的订单非待支付状态");
    }
    
    // 2. 防重复支付控制
    if (Objects.equals(plusOrderEntity.getPayType(), 
                      PlusOrderPayTypeEnum.PAY_AFTER.getValue())
            && redisUtils.hasKey(PLUS_ORDER_AFTER_HK + orderSn)) {
        throw new PlusAbyssException("您的会员订单正在支付中，请勿重复支付");
    }
    
    // 3. 生成分账信息
    PlusOrderDeductResEntity getResEntity = separateModel.getPlusOrderSeparateInfo(
            plusOrderEntity, deductPlan);
    
    // 4. 构建支付参数
    return buildOrderPayInfo(orderSn, plusOrderEntity);
}
```

### 3.2 支付参数结构分析

**PlusOrderPayInfoAo 核心字段**：

| 字段名 | 类型 | 必填 | 描述 | 来源 |
|--------|------|------|------|------|
| userId | String | 是 | 用户ID | 订单信息 |
| application | String | 是 | 业务渠道 | 订单渠道ID |
| orderSn | String | 是 | 会员订单号 | 订单主键 |
| configId | Integer | 是 | 会员类型ID | 订单配置 |
| totalAmount | BigDecimal | 是 | 总金额(元) | 分账计算结果 |
| tradeName | String | 是 | 交易名称 | 业务逻辑生成 |
| source | String | 是 | 来源标识 | 固定值"member-center" |
| businessScene | String | 是 | 业务场景 | 分流配置 |
| applySerialNo | String | 是 | 申请流水号 | 分账生成 |
| tradeType | String | 是 | 交易类型 | 固定值"R"(会员) |
| splitInfo | List | 否 | 分账信息 | 清分主体信息 |

**分账信息结构**：
```java
public static class DivideInfo {
    private String code;        // 分账识别码-商务识别码
    private BigDecimal amount;  // 分账金额
}
```

### 3.3 参数生成逻辑

**交易名称生成**：
```java
// 根据会员类型确定交易名称
String tradeName = JuziPlusEnum.RDZX_CARD.getCode() == plusOrderInfo.getConfigId() 
    ? "融担咨询卡" : "会员主动划扣";
```

**分账信息生成**：
```java
// 只返回清分主体的分账信息
separateEntity.getItems().forEach(item -> {
    if (item.getSupplierType().equals(SupplierTypeEnum.QF.getCode())) {
        DivideInfo divideInfo = new DivideInfo();
        divideInfo.setCode(item.getMerchantId());
        divideInfo.setAmount(item.getSeparateAmount());
        plusOrderPayInfoAo.getSplitInfo().add(divideInfo);
    }
});
```

## 4. 返回值结构分析

### 4.1 成功响应结构

**PlusOrderPayInfoResp 完整结构**：
```java
public class PlusOrderPayInfoResp {
    private String application;      // 业务渠道
    private String businessScene;    // 业务场景
    private String userId;          // 用户ID
    private String applySerialNo;   // 业务申请支付流水号
    private String tradeType;       // 交易类型 R(会员)
    private String source;          // 来源 member-center
    private BigDecimal totalAmount; // 总金额(元)
    private String orderSn;         // 会员订单号
    private Integer configId;       // 会员类型ID
    private String tradeName;       // 交易名称
    private List<DivideInfo> splitInfo; // 分账信息
}
```

**成功响应示例**：
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "userId": "12345",
        "application": "777",
        "orderSn": "PLUS1703123456789",
        "configId": 1,
        "totalAmount": 29.90,
        "tradeName": "会员主动划扣",
        "source": "member-center",
        "businessScene": "MEMBER_PAY",
        "applySerialNo": "AP1703123456789",
        "tradeType": "R",
        "splitInfo": [
            {
                "code": "MERCHANT_001",
                "amount": 29.90
            }
        ]
    }
}
```

### 4.2 失败响应结构

**常见错误场景**：

| 错误码 | 错误信息 | 场景描述 |
|--------|----------|----------|
| 400 | 会员订单号不能为空 | 参数校验失败 |
| 404 | 请求的订单不存在 | 订单不存在 |
| 409 | 请求的订单非待支付状态 | 订单状态异常 |
| 9999 | 您的会员订单正在支付中，请勿重复支付 | 重复支付控制 |
| 500 | 获取订单支付信息失败 | 分账信息生成失败 |

**失败响应示例**：
```json
{
    "code": 409,
    "message": "请求的订单非待支付状态",
    "data": null
}
```

## 5. 支付状态处理

### 5.1 支付状态枚举

**订单状态**：
```java
public enum PlusOrderStateEnum {
    WAIT_PAY(1, "待支付"),
    PAY_SUCCESS(2, "支付成功"), 
    CANCELED(3, "已取消")
}
```

**支付结果状态**：
```java
public enum PayStateEnum {
    S("S", "支付成功"),
    F("F", "支付失败"),
    QS("QS", "支付成功&扣额度成功"),
    QF("QF", "支付成功&扣额度失败")
}
```

**新支付系统状态**：
```java
public enum PayStateCodeEnum {
    S("S", "成功"),
    F("F", "失败"),
    I("I", "处理中")
}
```

### 5.2 支付状态流转

```plantuml
@startuml 支付状态流转
[*] --> 待支付 : 创建订单
待支付 --> 支付中 : 发起支付
支付中 --> 支付成功 : 支付完成
支付中 --> 支付失败 : 支付失败
支付成功 --> [*] : 开通会员
支付失败 --> 待支付 : 支持重新支付
待支付 --> 已取消 : 超时取消

note right of 待支付 : order_state = 1
note right of 支付成功 : order_state = 2
note right of 已取消 : order_state = 3
@enduml
```

### 5.3 状态更新逻辑

**支付成功处理**：
```java
@Transactional(rollbackFor = Exception.class)
public PlusPayCallbackAo payCallBack(PlusOrderPayCallbackEvent callbackEvent) {
    // 1. 校验订单存在性
    PlusOrderEntity plusOrderEntity = plusOrderQueryModel.getByOrderSn(
            callbackEvent.getOrderSn());
    ParamCheckUtils.checkNull(plusOrderEntity, "订单不存在");
    
    // 2. 处理支付回调
    switch (callbackEvent.getFlag()) {
        case CommonConstant.ONE:
            // 开卡逻辑
            plusOrderModel.payCallBackForOpenCard(callbackEvent, plusOrderEntity);
            // 发放权益
            memberPlusApplication.openCard(plusOrderEntity, programEntity);
            // 绑定订单关系
            bindOrderRelation(callbackEvent, plusOrderEntity);
            break;
    }
    
    return ao;
}
```

## 6. 异步回调处理机制

### 6.1 消息队列消费者

**支付结果回调监听器**：
```java
@Configuration
public class PayResultCallbackListener extends NormalConsumerClient {
    
    @Override
    protected boolean dealMessage(Message message, ConsumeContext consumeContext) {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        PayCallbackEntity entity = JSONObject.parseObject(body, PayCallbackEntity.class);
        orderApplication.payResultCallback(entity);
        return true;
    }
}
```

**新支付系统回调监听器**：
```java
@Configuration  
public class NewPayResultCallbackListener extends NormalConsumerClient {
    
    @Override
    protected boolean dealMessage(Message message, ConsumeContext consumeContext) {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        NewPayResultCallbackEntity entity = JSONObject.parseObject(body, 
                NewPayResultCallbackEntity.class);
        orderApplication.newPayResultCallback(entity);
        return true;
    }
}
```

### 6.2 回调数据结构

**支付回调实体**：
```java
public class NewPayResultCallbackEntity {
    private String serialNumber;    // 支付流水号
    private String thirdPayNum;     // 业务请求流水号
    private String orderId;         // 订单号
    private String state;           // 状态 S/F/I
    private String application;     // 渠道号
    private String payErrorCode;    // 支付错误码
    private String payErrorMsg;     // 支付错误描述
    private String payProductCode;  // 支付产品编码
    private String ctCardNo;        // 银行卡号
    private BigDecimal amount;      // 交易金额(元)
}
```

### 6.3 回调处理逻辑

**新支付系统回调处理**：
```java
public void newPayResultCallback(NewPayResultCallbackEntity entity) {
    // 1. 参数校验
    if (entity == null || StringUtils.isBlank(entity.getPayProductCode()) 
            || StringUtils.isBlank(entity.getOrderId())) {
        throw new PlusAbyssException("新支付系统回调结果数据异常");
    }
    
    // 2. 主动支付成功保存对账信息
    if (isActivePaymentSuccessful(entity)) {
        PlusOrderEntity byPlusOrderSn = plusOrderQueryModel.getByOrderSn(entity.getOrderId());
        orderBillModel.saveOrderBill(converter.toCreateOrderBillEvent(byPlusOrderSn));
    }
    
    // 3. 处理支付记录
    if (PayProductCodeEnum.HK.getCode().equals(entity.getPayProductCode())
            && PayStateCodeEnum.F.getCode().equals(entity.getState())) {
        payRecordModel.newPayFail(entity);
    }
    
    // 4. 支付成功处理
    if (PayStateCodeEnum.S.getCode().equals(entity.getState())) {
        orderShuntModel.newPaySuccessInSupplier(entity);
    }
    
    // 5. 划扣成功三方预入账
    if (PayProductCodeEnum.HK.getCode().equals(entity.getPayProductCode())
            && PayStateCodeEnum.S.getCode().equals(entity.getState())) {
        orderBillModel.newPayIncomeNotify(entity);
        payRecordModel.newPaySuccess(entity);
    }
    
    // 6. 小额月卡划扣失败处理
    xeykPayNotifyProcess(entity);
    
    // 7. 处理分账记录
    separateModel.payNotify(entity);
}
```

## 7. 异常场景处理

### 7.1 支付失败处理逻辑

**支付失败场景分类**：

| 失败类型 | 描述 | 处理策略 |
|----------|------|----------|
| 同步支付失败 | 调用支付接口直接返回失败 | 保持订单待支付状态，支持重新支付 |
| 异步支付失败 | 支付系统回调通知失败 | 更新支付记录状态，记录失败原因 |
| 网络超时 | 支付请求超时 | 主动查询支付状态，补偿处理 |
| 参数错误 | 支付参数校验失败 | 记录错误日志，返回具体错误信息 |

**支付失败处理代码**：
```java
// 支付失败处理
if (StringUtils.equals(entity.getPayType(), PayTypeEnum.DEDUCT.getCode())
        && StringUtils.equals(entity.getStatus(), PayStateEnum.F.getCode())) {
    payRecordModel.payFail(entity);
}

// 小额月卡划扣失败特殊处理
if (PayProductCodeEnum.HK.getCode().equals(entity.getPayProductCode())
        && PayStateCodeEnum.F.getCode().equals(entity.getState())) {
    String orderSn = entity.getOrderId();
    PlusOrderEntity order = plusOrderQueryModel.getByOrderSn(orderSn);

    if (order != null && order.getConfigId() == JuziPlusEnum.XEYK_CARD.getCode()) {
        // 首单划扣失败：无条件取消订单
        if (firstOrder) {
            PlusOrderCancelEvent cancelEvent = converter.toPlusOrderCancelEvent(order,
                    PlusCancelTypeEnum.NO_CONDITION.getValue(),
                    CancelReasonEnum.CANCEL_REASON_28.getCode(), 0, "system");
            orderApplication.cancelOrderApply(cancelEvent);
        } else {
            // 续费单划扣失败：取消续费计划
            CancelRenewEvent event = converter.toCancelRenewEvent(orderSn,
                    "续费单划扣失败自动取消续费", CommonConstant.THREE);
            cancelRenew(event);
        }
    }
}
```

### 7.2 超时和重试机制

**超时处理策略**：

1. **支付请求超时**：
   - 设置合理的超时时间（通常30-60秒）
   - 超时后主动查询支付状态
   - 根据查询结果决定后续处理

2. **回调超时处理**：
   - 设置回调超时监控
   - 超时后主动查询支付结果
   - 补偿更新订单状态

**主动查询支付状态**：
```java
// 收银台支付结果查询
public BaseResponse<CashierTradeQueryResponse> cashierTradeQuery(String thirdPayNum) {
    TradeQueryRequest tradeQueryRequest = new TradeQueryRequest();
    tradeQueryRequest.setThirdPayNum(thirdPayNum);
    return super.withCall(new AbstractCallContent<CashierTradeQueryResponse>(
            "/calm-cashier/cashier/tradeQuery", tradeQueryRequest) {
        @Override
        public BaseResponse<CashierTradeQueryResponse> deserialize(String result) {
            return JSON.parseObject(result,
                    new TypeReference<BaseResponse<CashierTradeQueryResponse>>(){});
        }
    });
}
```

**查询结果处理**：
```java
public class CashierTradeQueryResponse {
    private String state;              // 支付状态: S成功 F失败 I处理中 C已关闭 W待处理
    private String payProductCode;     // 支付产品编码
    private String errorCode;          // 错误码
    private String errorMsg;           // 错误信息
    private String tradeTime;          // 交易时间
    private boolean alipaySignStatus;  // 支付宝是否已签约
    private String bankCardId;         // 绑卡id
    private String serialNumber;       // 收银台流水号
    private String thirdPayNum;        // 渠道请求流水号
    private String orderId;            // 订单号
    private String paySerialNumber;    // 支付流水号
}
```

### 7.3 用户取消支付处理

**取消支付场景**：
1. 用户主动取消支付
2. 支付页面超时自动取消
3. 系统检测到异常主动取消

**取消处理逻辑**：
```java
@Override
public void unPayCancelCallBack(String plusOrderSn) {
    plusOrderModel.unPayCancelCallBack(plusOrderSn);
}

// 未支付超时取消处理
public void unPayCancelCallBack(String plusOrderSn) {
    PlusOrderEntity plusOrder = plusOrderQueryModel.getByOrderSn(plusOrderSn);
    if (plusOrder != null && plusOrder.getOrderState() == PlusOrderStateEnum.WAIT_PAY.getCode()) {
        // 更新订单状态为取消
        plusOrder.setOrderState(PlusOrderStateEnum.CANCELED.getCode());
        plusOrder.setCancelType(PlusCancelTypeEnum.EXPIRE_PAYMENT.getValue());
        // 保存订单状态变更
        plusOrderRepository.updateOrderState(plusOrder);
        // 处理支付记录
        payRecordModel.unPayCancel(plusOrderSn);
    }
}
```

## 8. 技术实现细节

### 8.1 幂等性控制

**支付信息获取幂等性**：
```java
// 分布式锁防止重复生成分账信息
String lockKey = PlusConcatUtils.symbolBelowStr(
        RedisConstantPrefix.PLUS_ORDER_QUERY_PAY_INFO, orderSn);
RLock rLock = redissonClient.getLock(lockKey);

try {
    boolean result = rLock.tryLock(2, TimeUnit.SECONDS);
    if (!result) {
        throw new PlusAbyssException("请求订单支付信息,加锁失败");
    }
    plusOrderPayInfoAo = buildOrderPayInfo(orderSn, plusOrderEntity);
} finally {
    if (rLock.isHeldByCurrentThread()) {
        rLock.unlock();
    }
}
```

**支付回调幂等性**：
```java
@Transactional(rollbackFor = Exception.class)
public PlusPayCallbackAo payCallBack(PlusOrderPayCallbackEvent callbackEvent) {
    // 检查订单状态，避免重复处理
    PlusOrderEntity plusOrderEntity = plusOrderQueryModel.getByOrderSn(
            callbackEvent.getOrderSn());

    if (plusOrderEntity.getOrderState() == PlusOrderStateEnum.PAY_SUCCESS.getCode()) {
        log.info("订单已处理，跳过重复回调：{}", callbackEvent.getOrderSn());
        return buildResult(plusOrderEntity);
    }

    // 执行业务逻辑
    // ...
}
```

### 8.2 缓存策略

**支付信息缓存**：
```java
// 设置支付信息缓存，避免重复计算
String key = PlusConcatUtils.symbolBelowStr(PLUS_ORDER_PAY_INFO, orderSn);
String result = redisUtils.get(key);
if (StringUtils.isNotBlank(result)) {
    log.info("缓存中查询订单支付信息,{}", orderSn);
    return JSONObject.parseObject(result, PlusOrderPayInfoAo.class);
}

// 生成支付信息后设置缓存
redisUtils.setEx(key, JSONObject.toJSONString(plusOrderPayInfoAo), 5, TimeUnit.SECONDS);
```

**重复支付防控**：
```java
// 后付款订单防重复支付
if (Objects.equals(plusOrderEntity.getPayType(), PlusOrderPayTypeEnum.PAY_AFTER.getValue())) {
    log.info("后付款订单主动支付加缓存标识,orderSn:{}", orderSn);
    redisUtils.setEx(PLUS_ORDER_AFTER_ZD + orderSn, "1",
            configProperties.afterPayLockTime, TimeUnit.SECONDS);
}
```

### 8.3 监控和日志

**关键节点日志**：
```java
// 支付信息生成日志
log.info("开始生成请求支付信息 orderSn {}", orderSn);
log.info("结束生成请求支付信息 {}", JSONObject.toJSONString(plusOrderPayInfoAo));

// 支付回调处理日志
log.info("会员订单支付回调处理开始：{}", JSON.toJSON(callbackEvent));
log.info("会员订单支付回调处理结束：{}", callbackEvent.getOrderSn());

// 新支付系统回调日志
log.info("新支付系统支付结果回调内容：{}", body);
log.info("新支付系统支付结果回调处理完成：{}", body);
```

**异常监控**：
```java
// 支付异常告警
try {
    // 支付处理逻辑
} catch (Exception e) {
    LogUtil.printLog("支付结果回调处理异常", e);
    imRepositoryAcl.sendImMessage("支付结果回调处理异常,请及时处理！");
    return false;
}
```

## 9. 业务流程优化建议

### 9.1 性能优化

1. **缓存优化**：
   - 支付信息缓存时间可适当延长
   - 增加分账信息缓存
   - 优化Redis键值设计

2. **并发优化**：
   - 优化分布式锁粒度
   - 使用异步处理非关键路径
   - 减少数据库查询次数

3. **接口优化**：
   - 合并多次数据库查询
   - 优化分账信息计算逻辑
   - 减少不必要的参数校验

### 9.2 可靠性提升

1. **重试机制**：
   - 增加支付状态查询重试
   - 优化MQ消费重试策略
   - 增加补偿任务机制

2. **监控告警**：
   - 增加支付成功率监控
   - 增加回调延迟监控
   - 增加异常订单告警

3. **数据一致性**：
   - 增强事务控制
   - 优化状态流转逻辑
   - 增加数据校验机制

### 9.3 用户体验优化

1. **支付体验**：
   - 优化支付页面加载速度
   - 增加支付进度提示
   - 优化错误提示信息

2. **异常处理**：
   - 提供更友好的错误提示
   - 增加支付失败重试引导
   - 优化支付状态同步

## 10. 总结

会员调用收银台支付业务流程是一个涉及多个系统协作的复杂流程，通过标准化的接口设计、完善的异常处理机制、可靠的异步回调处理，确保了支付的准确性和可靠性。

**核心特点**：
1. **标准化接口**：统一的参数结构和返回格式
2. **异步处理**：通过MQ实现支付结果的异步通知
3. **幂等控制**：多层次的幂等性保障机制
4. **异常处理**：完善的异常场景处理策略
5. **监控告警**：全链路的监控和异常告警

**技术亮点**：
- 分布式锁保证并发安全
- Redis缓存提升性能
- 事务控制保证数据一致性
- MQ异步处理提升用户体验
- 完善的日志和监控体系
```

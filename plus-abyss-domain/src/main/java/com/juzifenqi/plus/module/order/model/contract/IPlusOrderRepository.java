package com.juzifenqi.plus.module.order.model.contract;

import com.juzifenqi.plus.dto.req.admin.PlusOrderInfoQueryReq;
import com.juzifenqi.plus.dto.req.order.PlusOrderQueryReq;
import com.juzifenqi.plus.dto.req.order.PlusOutOrderQueryReq;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusInfoDetailEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusInfoEntity;
import com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoDetailExtPo;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderCancelEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderExtInfoEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderResubmitFlagEntity;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderCancelEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderCreateEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderPayCallbackEvent;
import com.juzifenqi.plus.module.order.repository.po.PlusOrderInfoPo;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface IPlusOrderRepository {

    /**
     * 根据订单号获取订单数据
     */
    PlusOrderEntity  getByPlusOrderSn(String plusOrderSn);

    /**
     * 根据订单号获取扩展数据
     */
    PlusOrderExtInfoEntity getOrderExtInfo(String plusOrderSn);

    /**
     * 根据多个订单号获取扩展数据
     */
    List<PlusOrderExtInfoEntity> getOrderExtInfoList(List<String> plusOrderSns);

    /**
     * 保存订单实体
     */
    void saveOrder(PlusOrderEntity plusOrderEntity, MemberPlusInfoEntity memberPlusInfo,
            PlusOrderCreateEvent plusOrderCreateEvent);

    /**
     * 是否首次购买
     */
    int getNumByUserAndProId(Integer memberId, Integer programId);

    /**
     * 计算订单取消需要扣减的数据信息
     */
    PlusOrderCancelEntity sumDeductPrice(PlusOrderCancelEvent plusOrderCancelEvent,
            PlusOrderEntity plusOrderEntity);

    /**
     * 取消会员订单相关
     */
    void cancelOrder(PlusOrderCancelEvent event, PlusOrderCancelEntity entity);

    /**
     * 变更会员订单状态
     */
    void updateOrderState(String orderSn, Integer orderState);

    /**
     * 取消会员订单相关计划（后付款划扣计划、短信通知/提醒计划、融担咨询卡拉取账单计划等）
     */
    void cancelPlan(PlusOrderCancelEvent event, PlusOrderEntity plusOrderEntity);

    /**
     * 会员订单支付成功处理
     */
    void orderPaySuccess(PlusOrderPayCallbackEvent callbackEvent, PlusOrderEntity plusOrderEntity,
            MemberPlusInfoDetailEntity memberPlusInfoDetail);

    /**
     * 后付款会员开通成功处理
     */
    void afterOderOpenCard(PlusOrderEntity plusOrderEntity, MemberPlusInfoDetailEntity detail);

    /**
     * 计算指定状态下订单数据条数
     */
    Integer countByOrderListAndState(List<String> orderList, Integer orderState);

    /**
     * 计算指定状态下订单数据支付金额
     */
    BigDecimal sumPayAmountByOrderListAndState(List<String> orderList, Integer orderState);

    /**
     * 移动订单周期
     */
    void moveOrderPeriod(List<MemberPlusInfoDetailExtPo> moveList);

    /**
     * 计算退款金额，包含扣减差价计算
     */
    PlusOrderCancelEntity calOrderRefundAmount(PlusOrderEntity order, PlusOrderCancelEvent event);

    /**
     * 根据多个订单号批量获取订单数据
     */
    List<PlusOrderEntity> getPlusOrderBySns(List<String> plusOrderSns);


    /**
     * 获取用户待支付的后付款订单列表(排除融担卡)
     */
    List<PlusOrderEntity> getUserWaitPayOrderList(Integer userId, Integer channelId,
            Integer programId);


    /**
     * 获取用户待支付的后付款订单列表(排除融担卡)
     */
    List<PlusOrderEntity> getUserWaitPayOrderList(Integer userId, Integer channelId);


    List<PlusOrderEntity> getUserWaitPayOrderListByCondition(Integer userId, Integer channelId, List<Integer> payTypes);

    /**
     * 获取用户订单列表(configId不传会排除融担卡)
     */
    List<PlusOrderEntity> getUserOrderList(PlusOrderQueryReq query);

    /**
     * 用户7天内购买过的会员订单列表
     */
    List<PlusOrderEntity> getUserSevenOrderList(Integer userId, Integer channelId,
            Integer... configIds);

    /**
     * 认证成功后购买过的会员
     */
    List<PlusOrderEntity> getPlusOrderByAuthTime(Integer userId, Integer channelId,
            Integer... configIds);

    /**
     * 获取待支付的后付款订单列表
     */
    List<PlusOrderEntity> getWaitPayOrderList(List<String> orders);

    /**
     * 根据订单号批量获取
     */
    List<PlusOrderEntity> getOrderList(List<String> orders);
    /**
     * 过期会员批量取消订单
     */
    void expireCancelOrder(List<String> orderSns);

    /**
     * 获取用户待支付的后付款订单
     */
    List<PlusOrderEntity> getAfterPayOrderInfoList(Integer userId, List<Integer> configIds);

    /**
     * 获取7天内下过的单
     */
    List<PlusOrderEntity> getSevenPlusOrder(Integer userId, Integer channelId,
            List<Integer> configIds);

    /**
     * 分页查询-带状态
     */
    List<PlusOrderEntity> pageListByState(PlusOrderInfoQueryReq req);

    /**
     * 分页查询Count-带状态
     */
    Integer pageListCountByState(PlusOrderInfoQueryReq req);

    /**
     * 分页查询
     */
    List<PlusOrderEntity> pageList(PlusOrderInfoQueryReq req);

    /**
     * 分页查询Count
     */
    Integer pageListCount(PlusOrderInfoQueryReq req);


    /**
     * 获取用户某个会员卡有效的待支付的后付款订单
     */
    PlusOrderEntity getUserWaitPayOrder(Integer userId, Integer configId);

    /**
     * 获取用户某个会员卡有效的待支付的后付款订单
     */
    PlusOrderEntity getUserWaitPayOrderAfterAuthTime(Integer userId, Integer configId,Date authTime);

    /**
     * 查询最新的40条支付成功订单列表
     */
    List<PlusOrderEntity> getPayRecordByProgram(Integer programId);

    /**
     * 获取初始化订单集合
     */
    List<PlusOrderResubmitFlagEntity> getToBeVerifyList();

    /**
     * 批量修改状态为处理中
     */
    Integer batchProcessing(List<Integer> ids);

    /**
     * 更新订单重提标识
     */
    Integer updatePlusOrderResubmitFlag(PlusOrderResubmitFlagEntity plusOrderResubmitFlag);

    /**
     * 获取用户待支付的订单
     */
    List<PlusOrderEntity> getWaitPayOrderList(Integer userId, List<Integer> configIdList);

    /**
     * 获取7天内购买的有效会员订单信息
     */
    List<PlusOrderEntity> getMemberSevenOrderList(Integer userId, Integer channelId);

    /**
     * 获取认证成功后购买的有效会员订单信息
     */
    List<PlusOrderEntity> getPlusOrderInfoListByAuthTime(Integer userId, Integer channelId, Date authTime);

    /**
     * 获取待支付后付款订单信息
     */
    List<PlusOrderEntity> getUserWaitPayOrderList(PlusOrderQueryReq req);

    /**
     * 根据开始id，最大id以及批次大小查询记录
     * @param startId
     * @param maxId
     * @param batchSize
     * @return
     */
    List<PlusOrderEntity> getPlusOrderListByIdLimit(Integer startId,Integer maxId,Integer batchSize);

    /**
     * 根据id的集合查询记录
     * @param ids
     * @return
     */
    List<PlusOrderEntity> getPlusOrderByIds(List<Integer> ids);



    PlusOrderEntity getPlusOutOrder(PlusOutOrderQueryReq req);

    void updatePlusOrderInfoByOrderSn(PlusOrderInfoPo plusOrderInfoPo);
}

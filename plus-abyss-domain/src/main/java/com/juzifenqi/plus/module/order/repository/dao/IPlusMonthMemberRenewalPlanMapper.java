package com.juzifenqi.plus.module.order.repository.dao;

import com.juzifenqi.plus.module.order.repository.po.PlusMonthMemberRenewalPlanPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 会员月卡续费计划Mapper
 *
 * <AUTHOR>
 * @date 2025-8-18
 */
@Mapper
public interface IPlusMonthMemberRenewalPlanMapper {

    /**
     * 新增返回ID
     *
     * @param po 续费计划Po
     * @return 主键ID
     */
    Integer save(PlusMonthMemberRenewalPlanPo po);

    /**
     * 批量插入
     *
     * @param list 续费计划Po列表
     * @return 插入成功的记录数
     */
    Integer batchInsert(@Param("list") List<PlusMonthMemberRenewalPlanPo> list);

    /**
     * 根据订单号查询
     *
     * @param orderSn 会员订单号
     * @return 续费计划Po列表
     */
    List<PlusMonthMemberRenewalPlanPo> getByOrderSn(@Param("orderSn") String orderSn);

    /**
     * 分页查询续费计划记录
     *
     * @param planTime 计划生成时间
     * @param planStates 计划状态列表
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 续费计划Po列表
     */
    List<PlusMonthMemberRenewalPlanPo> pageQuery(@Param("planTime") Date planTime,
                                                  @Param("planStates") List<Integer> planStates,
                                                  @Param("offset") Integer offset,
                                                  @Param("limit") Integer limit);

    /**
     * 更新计划状态
     *
     * @param id 主键ID
     * @param planState 计划状态
     * @param remark 备注
     * @return 更新记录数
     */
    Integer updatePlanState(@Param("id") Integer id,
                           @Param("planState") Integer planState,
                           @Param("remark") String remark);

    /**
     * 同时更新计划状态、备注和实际生成时间
     *
     * @param id 主键ID
     * @param orderSn 订单号
     * @param planState 计划状态
     * @param remark 备注
     * @param actualPlanTime 实际生成时间
     * @return 更新记录数
     */
    Integer updatePlanStateAndActualTime(@Param("id") Integer id,
                                        @Param("orderSn") String orderSn,
                                        @Param("planState") Integer planState,
                                        @Param("remark") String remark,
                                        @Param("actualPlanTime") Date actualPlanTime);

    /**
     * 取消续费计划
     *
     * @param orderSn 订单号
     */
    Integer cancelRenewalPlan(@Param("orderSn") String orderSn);
}

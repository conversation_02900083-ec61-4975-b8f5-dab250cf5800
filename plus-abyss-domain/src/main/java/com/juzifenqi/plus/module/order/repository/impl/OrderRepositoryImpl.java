package com.juzifenqi.plus.module.order.repository.impl;

import com.alibaba.fastjson.JSON;
import com.juzifenqi.plus.config.ConfigProperties;
import com.juzifenqi.plus.constants.CommonConstant;
import com.juzifenqi.plus.dto.pojo.CreateOrderContext;
import com.juzifenqi.plus.dto.req.admin.PlusOrderInfoQueryReq;
import com.juzifenqi.plus.dto.req.order.PlusOrderQueryReq;
import com.juzifenqi.plus.dto.req.order.PlusOutOrderQueryReq;
import com.juzifenqi.plus.enums.*;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusInfoDetailEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusInfoEntity;
import com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoDetailExtPo;
import com.juzifenqi.plus.module.common.IMemberPlusSystemLogRepository;
import com.juzifenqi.plus.module.common.entity.MemberPlusSystemLogEntity;
import com.juzifenqi.plus.module.common.repository.external.acl.AuthExternalRepositoryAcl;
import com.juzifenqi.plus.module.order.model.PlusOrderSnapModel;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderRepository;
import com.juzifenqi.plus.module.order.model.contract.entity.OrderDeductResultEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderCancelEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderExtInfoEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderResubmitFlagEntity;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderCancelEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderCreateEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderPayCallbackEvent;
import com.juzifenqi.plus.module.order.model.impl.strategy.HandlerContext;
import com.juzifenqi.plus.module.order.repository.converter.IPlusOrderConverter;
import com.juzifenqi.plus.module.order.repository.dao.IPlusAfterOrderRemindMapper;
import com.juzifenqi.plus.module.order.repository.dao.IPlusOrderDeductPlanMapper;
import com.juzifenqi.plus.module.order.repository.dao.IPlusOrderExtInfoMapper;
import com.juzifenqi.plus.module.order.repository.dao.IPlusOrderInfoMapper;
import com.juzifenqi.plus.module.order.repository.dao.IPlusOrderPayDetailMapper;
import com.juzifenqi.plus.module.order.repository.dao.IPlusOrderResubmitFlagMapper;
import com.juzifenqi.plus.module.order.repository.dao.IPlusRdzxRepayPlanMapper;
import com.juzifenqi.plus.module.order.repository.impl.strategy.OrderDeductHandlerContext;
import com.juzifenqi.plus.module.order.repository.po.PlusAfterOrderRemindPo;
import com.juzifenqi.plus.module.order.repository.po.PlusOrderExtInfoPo;
import com.juzifenqi.plus.module.order.repository.po.PlusOrderInfoPo;
import com.juzifenqi.plus.module.order.repository.po.PlusOrderPayDetailPo;
import com.juzifenqi.plus.module.order.repository.po.PlusOrderResubmitFlagPo;
import com.juzifenqi.plus.module.order.repository.po.PlusRdzxRepayPlanPo;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

import com.jzfq.auth.core.entity.AuthApproval;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderRepositoryImpl implements IPlusOrderRepository {

    @Autowired
    private IPlusOrderInfoMapper           plusOrderInfoMapper;
    @Autowired
    private IPlusOrderExtInfoMapper        plusOrderExtInfoMapper;
    @Autowired
    private IPlusOrderResubmitFlagMapper   plusOrderResubmitFlagMapper;
    @Autowired
    private HandlerContext                 handlerContext;
    @Autowired
    private IMemberPlusSystemLogRepository memberPlusSystemLogRepository;
    @Autowired
    private IPlusAfterOrderRemindMapper    plusAfterOrderRemindMapper;
    @Autowired
    private IPlusRdzxRepayPlanMapper       plusRdzxRepayPlanMapper;
    @Autowired
    private IPlusOrderDeductPlanMapper     plusOrderDeductPlanMapper;
    @Autowired
    private IPlusOrderPayDetailMapper      plusOrderPayDetailMapper;
    @Autowired
    private OrderDeductHandlerContext      orderDeductHandlerContext;
    @Autowired
    private PlusOrderSnapModel             plusOrderSnapModel;
    @Autowired
    private ConfigProperties configProperties;
    @Autowired
    private AuthExternalRepositoryAcl authExternalRepositoryAcl;

    private final IPlusOrderConverter plusOrderConverter = IPlusOrderConverter.instance;


    /**
     * 根据订单号获取订单数据
     */
    @Override
    public PlusOrderEntity getByPlusOrderSn(String plusOrderSn) {
        PlusOrderInfoPo plusOrderInfoPo = plusOrderInfoMapper.getByOrderSn(plusOrderSn);
        PlusOrderExtInfoPo plusOrderExtInfoPo = plusOrderExtInfoMapper.getByOrderSn(plusOrderSn);
        return plusOrderConverter.toPlusOrderEntity(plusOrderInfoPo, plusOrderExtInfoPo);
    }

    /**
     * 根据订单号获取扩展数据
     */
    @Override
    public PlusOrderExtInfoEntity getOrderExtInfo(String plusOrderSn) {
        PlusOrderExtInfoPo plusOrderExtInfoPo = plusOrderExtInfoMapper.getByOrderSn(plusOrderSn);
        return plusOrderConverter.toPlusExtInfoEntity(plusOrderExtInfoPo);
    }

    @Override
    public List<PlusOrderExtInfoEntity> getOrderExtInfoList(List<String> plusOrderSns) {
        List<PlusOrderExtInfoPo> plusOrderExtInfoPos = plusOrderExtInfoMapper.selectByOrderSns(
                plusOrderSns);
        return plusOrderConverter.toPlusExtInfoEntityList(plusOrderExtInfoPos);
    }

    /**
     * 保存订单实体
     */
    @Override
    public void saveOrder(PlusOrderEntity plusOrderEntity, MemberPlusInfoEntity memberPlusInfo,
            PlusOrderCreateEvent plusOrderCreateEvent) {
        // 订单主表
        saveOrderInfo(plusOrderEntity, memberPlusInfo, plusOrderCreateEvent);
        // 扩展表
        saveExtInfo(plusOrderEntity, plusOrderCreateEvent);
        // 保存订单重提标识
        saveOrderResubmitFlag(plusOrderEntity);
        // 保存订单-会员权益快照
        saveOrderSnapshot(plusOrderEntity);
        // 个性化
        handlerContext.createOrderAfter(plusOrderEntity, plusOrderCreateEvent);
    }

    /**
     * 保存订单-会员权益快照
     */
    private void saveOrderSnapshot(PlusOrderEntity plusOrderEntity) {
        plusOrderSnapModel.createPlusOrderSnapshot(plusOrderEntity);
    }

    /**
     * 保存订单主表
     */
    private void saveOrderInfo(PlusOrderEntity plusOrderEntity, MemberPlusInfoEntity memberPlusInfo,
            PlusOrderCreateEvent event) {
        PlusOrderInfoPo plusOrderInfo = new PlusOrderInfoPo();
        plusOrderInfo.setOrderSn(plusOrderEntity.getOrderSn());
        plusOrderInfo.setPayType(plusOrderEntity.getPayType());
        if (memberPlusInfo != null) {
            plusOrderInfo.setOrderType(CommonConstant.TWO);
        } else {
            plusOrderInfo.setOrderType(CommonConstant.ONE);
        }
        plusOrderInfo.setOrderState(PlusOrderStateEnum.WAIT_PAY.getCode());
        // 20231120 zjf 开通类型=全款 支付方式=划扣，订单状态=支付成功
        CreateOrderContext context = event.getCreateOrderContext();
        if (event.getPayType() == CommonConstant.ONE && context != null
                && context.getPayType() != null && context.getPayType() == CommonConstant.ONE) {
            log.info("保存会员订单,全款且划扣订单状态为支付成功：{}", plusOrderInfo.getOrderSn());
            plusOrderInfo.setOrderState(PlusOrderStateEnum.PAY_SUCCESS.getCode());
        }
        plusOrderInfo.setProgramId(plusOrderEntity.getProgramId());
        plusOrderInfo.setOrderAmount(plusOrderEntity.getOrderAmount());
        plusOrderInfo.setProgramName(plusOrderEntity.getProgramName());
        plusOrderInfo.setChannelId(plusOrderEntity.getChannelId());
        plusOrderInfo.setBizSource(plusOrderEntity.getBizSource());
        plusOrderInfo.setConfigId(plusOrderEntity.getConfigId());
        plusOrderInfo.setRemark("");
        plusOrderInfo.setUserId(plusOrderEntity.getUserId());
        plusOrderInfo.setProgramPrice(plusOrderEntity.getProgramPrice());
        plusOrderInfo.setDiscountRate(plusOrderEntity.getDiscountRate());
        // 设置首付金额（分期支付场景）
        if (plusOrderEntity.getFirstPayAmount() != null) {
            plusOrderInfo.setFirstPayAmount(plusOrderEntity.getFirstPayAmount());
        }
        plusOrderInfoMapper.insertPlusOrderInfo(plusOrderInfo);
    }

    /**
     * 保存扩展信息
     */
    private void saveExtInfo(PlusOrderEntity plusOrderEntity, PlusOrderCreateEvent event) {
        // 扩展表
        PlusOrderExtInfoPo extInfo = new PlusOrderExtInfoPo();
        extInfo.setOrderSn(plusOrderEntity.getOrderSn());
        if (plusOrderEntity.getCreateOrderContext() != null) {
            extInfo.setOrderAmount(plusOrderEntity.getCreateOrderContext().getPlusAmount());
            extInfo.setVirtualAmount(plusOrderEntity.getCreateOrderContext().getLmkAmount());
        }
        extInfo.setUserId(plusOrderEntity.getUserId());
        extInfo.setChannelId(plusOrderEntity.getChannelId());
        // 订单业务类型处理，如果为空，则默认为普通订单
        extInfo.setBusinessType(
                plusOrderEntity.getBusinessType() == null ? BusinessTypeEnum.DEFAULT.getCode()
                        : plusOrderEntity.getBusinessType());
        extInfo.setOrderFlag(OrderFlagEnum.NEW_ORDER.getCode());
        // 20231110 zjf 支付方式，支付流水号、三方单号存储
        if (event.getCreateOrderContext() != null) {
            extInfo.setPaySuccessReturnUrl(event.getCreateOrderContext().getPaySuccessReturnUrl());
            extInfo.setSerialNumber(event.getCreateOrderContext().getSerialNumber());
            extInfo.setPayType(event.getCreateOrderContext().getPayType());
        }
        extInfo.setOutOrderSn(event.getOutOrderSn());
        plusOrderExtInfoMapper.insert(extInfo);
    }

    /**
     * 保存当前订单的重提标识
     */
    private void saveOrderResubmitFlag(PlusOrderEntity plusOrderEntity) {
        String orderSn = plusOrderEntity.getOrderSn();
        Integer configId = plusOrderEntity.getConfigId();
        log.info("保存当前订单的重提标识开始，orderId：{},configId:{}", orderSn, configId);
        //全部卡类型都需要保存重提标识
        PlusOrderResubmitFlagPo resubmitFlag = new PlusOrderResubmitFlagPo();
        resubmitFlag.setOrderSn(orderSn);
        resubmitFlag.setConfigId(configId);
        resubmitFlag.setUserId(plusOrderEntity.getUserId());
        resubmitFlag.setChannelId(plusOrderEntity.getChannelId());
        plusOrderResubmitFlagMapper.savePlusOrderResubmitFlag(resubmitFlag);
        log.info("保存当前订单的重提标识结束，orderId：{},configId:{}", orderSn, configId);
    }

    @Override
    public int getNumByUserAndProId(Integer memberId, Integer programId) {
        return plusOrderInfoMapper.getNumByUserAndProId(memberId, programId);
    }

    @Override
    public PlusOrderCancelEntity sumDeductPrice(PlusOrderCancelEvent plusOrderCancelEvent,
            PlusOrderEntity plusOrderEntity) {
        PlusOrderCancelEntity entity = new PlusOrderCancelEntity();
        String plusOrderSn = plusOrderCancelEvent.getPlusOrderSn();
        log.info("计算会员退费扣除差价开始：{}", plusOrderSn);
        // 20230511 zjf 是否联名卡会员
        PlusOrderExtInfoPo extInfo = plusOrderExtInfoMapper.getByOrderSn(plusOrderSn);
        boolean lmk =
                extInfo != null && BusinessTypeEnum.LMK.getCode().equals(extInfo.getBusinessType());
        if (lmk) {
            entity.setMoneyBack(extInfo.getOrderAmount());
            entity.setLmkPlusAmount(extInfo.getOrderAmount());
            entity.setLmkVirtualAmount(extInfo.getVirtualAmount());
        } else {
            entity.setMoneyBack(plusOrderEntity.getOrderAmount());
        }
        // 会员金额
        BigDecimal orderAmount = entity.getMoneyBack();
        entity.setDeductPrice(BigDecimal.ZERO);
        // 差价
        BigDecimal deductPrice = BigDecimal.ZERO;
        // 使用的权益数量
        int virtualNum = 0;
        List<OrderDeductResultEntity> deductList = new ArrayList<>(4);
        // 20240219 zjf 计算差价重构
        for (String code : OrderDeductItemEnum.getCodes()) {
            OrderDeductResultEntity result = orderDeductHandlerContext.calOrderDeductPrice(
                    plusOrderCancelEvent, plusOrderEntity, code);
            // 累加差价和权益数量
            virtualNum += result.getProfitsNum();
            deductPrice = deductPrice.add(result.getDeductPrice());
            // 购物返现处理
            if (code.equals(OrderDeductItemEnum.GWFX.getCode())
                    && result.getDeductPrice().compareTo(BigDecimal.ZERO) > 0) {
                entity.setUseGwfx(true);
                entity.setGwfxAmount(result.getDeductPrice());
            }
            deductList.add(result);
        }
        entity.setDeductList(deductList);
        log.info("计算订单退款差价总额：{},{}", plusOrderSn, deductPrice);
        // 计算退款金额等信息
        BigDecimal scaleDeduct = deductPrice.setScale(2, RoundingMode.HALF_DOWN);
        if (scaleDeduct.compareTo(BigDecimal.ZERO) > 0) {
            entity.setNeedDeductPrice(true);
            entity.setProfitNum(virtualNum);
            entity.setDeductPrice(scaleDeduct);
            entity.setMoneyBack(orderAmount.subtract(scaleDeduct));
            entity.setExceedPlusPrice(scaleDeduct.compareTo(orderAmount) >= 0);
        }
        log.info("计算会员退费扣除差价返回：{}", JSON.toJSONString(entity));
        return entity;
    }

    @Override
    public void cancelOrder(PlusOrderCancelEvent event, PlusOrderCancelEntity entity) {
        String orderSn = event.getPlusOrderSn();
        log.info("取消会员订单开始：{}", orderSn);
        // 取消订单主表
        PlusOrderInfoPo plusOrder = plusOrderInfoMapper.getByOrderSn(orderSn);
        if (plusOrder == null) {
            log.info("取消会员订单未查询到会员订单信息：{}", orderSn);
            return;
        }
        String reason = CancelReasonEnum.getNameByCode(event.getCancelReason());
        String cancelReason =
                StringUtils.isNotBlank(event.getRemark()) ? reason + "_" + event.getRemark()
                        : reason;
        // 更新付费会员订单状态
        PlusOrderInfoPo plusOrderInfo = new PlusOrderInfoPo();
        plusOrderInfo.setOrderSn(orderSn);
        plusOrderInfo.setCancelType(event.getCancelType());
        plusOrderInfo.setOrderState(PlusOrderStateEnum.CANCELED.getCode());
        plusOrderInfo.setCancelReason(cancelReason);
        // 长度处理
        if (StringUtils.isNotBlank(cancelReason) && cancelReason.length() > 100) {
            plusOrderInfo.setCancelReason(cancelReason.substring(0, 100));
        }
//        if (PlusOrderStateEnum.WAIT_PAY.getCode() != plusOrder.getOrderState()) {
//            plusOrderInfo.setRefundAmount(entity.getMoneyBack());
//        }
        if (entity != null) {
            plusOrderInfo.setRefundAmount(entity.getMoneyBack());
        }
        plusOrderInfoMapper.updatePlusOrderInfoByOrderSn(plusOrderInfo);
    }

    @Override
    public void updateOrderState(String orderSn, Integer orderState) {
        PlusOrderInfoPo plusOrderInfoPo = new PlusOrderInfoPo();
        plusOrderInfoPo.setOrderState(orderState);
        plusOrderInfoPo.setOrderSn(orderSn);
        plusOrderInfoMapper.updatePlusOrderInfo(plusOrderInfoPo);
    }

    @Override
    public void cancelPlan(PlusOrderCancelEvent event, PlusOrderEntity plusOrderEntity) {
        log.info("取消会员订单相关计划开始：{}", event.getPlusOrderSn());
        // 只有后付款的需要处理相关计划
        if (!PlusOrderPayTypeEnum.PAY_AFTER.getValue().equals(plusOrderEntity.getPayType())) {
            return;
        }
        // 取消短信通知
        PlusAfterOrderRemindPo plusAfterOrderRemind = new PlusAfterOrderRemindPo();
        plusAfterOrderRemind.setOrderSn(event.getPlusOrderSn());
        plusAfterOrderRemind.setState(CommonConstant.THREE);
        plusAfterOrderRemindMapper.updatePlusAfterOrderRemindByOrderSn(plusAfterOrderRemind);
        // 取消划扣计划
        plusOrderDeductPlanMapper.invalidDeductPlan(event.getPlusOrderSn());
        // 取消延迟短信发送计划
        plusOrderDeductPlanMapper.invalidMsgPlan(event.getPlusOrderSn());
        if (JuziPlusEnum.RDZX_CARD.getCode() == event.getConfigId()) {
            log.info("取消融担咨询卡相关计划开始：{}", event.getPlusOrderSn());
            // 取消账单拉取计划
            PlusRdzxRepayPlanPo plusRdzxRepayPlan = new PlusRdzxRepayPlanPo();
            plusRdzxRepayPlan.setPlusOrderSn(event.getPlusOrderSn());
            plusRdzxRepayPlan.setOptStatus(CommonConstant.THREE);
            plusRdzxRepayPlanMapper.updatePlusRdzxRepayPlanCancel(plusRdzxRepayPlan);
        }
    }

    @Override
    public void orderPaySuccess(PlusOrderPayCallbackEvent callbackEvent,
            PlusOrderEntity plusOrderEntity, MemberPlusInfoDetailEntity memberPlusInfoDetail) {
        String orderSn = callbackEvent.getOrderSn();
        log.info("会员订单支付成功处理开始：{}", orderSn);
        PlusOrderInfoPo update = new PlusOrderInfoPo();
        update.setOrderSn(orderSn);
        if (memberPlusInfoDetail != null) {
            update.setStartTime(memberPlusInfoDetail.getJxStartTime());
            update.setEndTime(memberPlusInfoDetail.getJxEndTime());
        }
        // 分期支付订单，首次支付，修改payAmount为firstPayAmount，不更改订单状态
        if (Objects.equals(plusOrderEntity.getPayType(), PlusOrderPayTypeEnum.PAY_FIRST_PERIOD.getValue())) {
            log.info("分期支付订单，首次支付，修改payAmount为firstPayAmount:{}", plusOrderEntity.getFirstPayAmount());
            update.setPayAmount(plusOrderEntity.getFirstPayAmount());
        } else {
            update.setPayAmount(plusOrderEntity.getOrderAmount());
            update.setOrderState(PlusOrderStateEnum.PAY_SUCCESS.getCode());
        }
        update.setPayTime(new Date());
        update.setCallTime(new Date());
        plusOrderInfoMapper.updatePlusOrderInfoByOrderSn(update);
        // 后付款订单处理
        if (Objects.equals(PlusOrderPayTypeEnum.PAY_AFTER.getValue(),
                plusOrderEntity.getPayType())) {
            // 日志
            MemberPlusSystemLogEntity plusLog = new MemberPlusSystemLogEntity();
            plusLog.memberId(plusOrderEntity.getUserId()).cancelRemark("收到支付成功回调-更改订单状态")
                    .channelId(plusOrderEntity.getChannelId())
                    .programId(plusOrderEntity.getProgramId()).orderSn(orderSn);
            memberPlusSystemLogRepository.saveMemberPlusLogByUser(plusLog,
                    LogNodeEnum.LOG_NODE_ORDER_PAY);
            // 更新后付款短信提醒
            PlusOrderCancelEvent event = new PlusOrderCancelEvent();
            event.setConfigId(plusOrderEntity.getConfigId());
            event.setPlusOrderSn(plusOrderEntity.getOrderSn());
            cancelPlan(event, plusOrderEntity);
            // 更新主动划扣记录表
            List<PlusOrderPayDetailPo> list = plusOrderPayDetailMapper.loadByPlusOrderSn(orderSn);
            log.info("查询已存在的支付信息,info:{}", JSON.toJSONString(list));
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            //流水号是空、不是空并且不相等，需要新增一条
            boolean insert = true;
            for (PlusOrderPayDetailPo detail : list) {
                if (detail.getTradeSn() == null) {
                    continue;
                }
                if (!detail.getTradeSn().equals(callbackEvent.getSerialNumber())) {
                    //修改原有效数据为失效
                    log.info("已存在的流水号和回调不一致,user:{},order:{}", plusOrderEntity.getUserId(),
                            orderSn);
                    plusOrderPayDetailMapper.invalidDetail(detail.getId());
                    continue;
                }
                insert = false;
            }
            if (insert) {
                //新增一条记录
                PlusOrderPayDetailPo plusOrderPayDetail = new PlusOrderPayDetailPo();
                plusOrderPayDetail.setConfigId(plusOrderEntity.getConfigId());
                plusOrderPayDetail.setProgramName(plusOrderEntity.getProgramName());
                plusOrderPayDetail.setProgramId(plusOrderEntity.getProgramId());
                plusOrderPayDetail.setChannelId(plusOrderEntity.getChannelId());
                plusOrderPayDetail.setOrderAmount(plusOrderEntity.getOrderAmount());
                plusOrderPayDetail.setOrderSn(plusOrderEntity.getOrderSn());
                plusOrderPayDetail.setOrderType(CommonConstant.ONE);
                plusOrderPayDetail.setPayState(CommonConstant.ONE);
                plusOrderPayDetail.setTradeSn(callbackEvent.getSerialNumber());
                plusOrderPayDetail.setPayType(PlusPayTypeEnum.PAY_TYPE_3.getCode());
                plusOrderPayDetail.setRemark(PlusPayTypeEnum.PAY_TYPE_3.getName());
                plusOrderPayDetail.setUserId(plusOrderEntity.getUserId());
                plusOrderPayDetailMapper.savePlusOrderPayDetail(plusOrderPayDetail);
                log.info("保存新的支付信息结束,user:{},order:{}", callbackEvent.getMemberId(), orderSn);
            }

        }
    }

    @Override
    public void afterOderOpenCard(PlusOrderEntity plusOrderEntity,
            MemberPlusInfoDetailEntity detail) {
        log.info("后付款订单开通成功更新订单信息开始：{}", plusOrderEntity.getOrderSn());
        PlusOrderInfoPo update = new PlusOrderInfoPo();
        update.setOrderSn(plusOrderEntity.getOrderSn());
        update.setStartTime(detail.getJxStartTime());
        update.setEndTime(detail.getJxEndTime());
        plusOrderInfoMapper.updatePlusOrderInfoByOrderSn(update);
        log.info("后付款订单开通成功更新订单信息成功：{}", plusOrderEntity.getOrderSn());
    }

    /**
     * 计算指定状态下订单数据条数
     */
    @Override
    public Integer countByOrderListAndState(List<String> orderList, Integer orderState) {
        return plusOrderInfoMapper.countByOrderListAndState(orderList, orderState);
    }

    /**
     * 计算指定状态下订单数据支付金额
     */
    @Override
    public BigDecimal sumPayAmountByOrderListAndState(List<String> orderList, Integer orderState) {
        BigDecimal payAmount = plusOrderInfoMapper.sumPayAmountByOrderListAndState(orderList,
                orderState);
        return payAmount != null ? payAmount : BigDecimal.ZERO;
    }

    @Override
    public void moveOrderPeriod(List<MemberPlusInfoDetailExtPo> moveList) {
        if (CollectionUtils.isEmpty(moveList)) {
            log.info("没有需要移动周期的订单列表");
            return;
        }
        log.info("移动订单周期开始：{}", JSON.toJSONString(moveList));
        plusOrderInfoMapper.updateBatchPeriods(moveList);
    }

    @Override
    public PlusOrderCancelEntity calOrderRefundAmount(PlusOrderEntity order,
            PlusOrderCancelEvent event) {
        String orderSn = order.getOrderSn();
        log.info("计算订单退款金额开始：{}", orderSn);
        PlusOrderCancelEntity entity = new PlusOrderCancelEntity();
        // 后付款待支付订单退0元
        if (order.getOrderState() == PlusOrderStateEnum.WAIT_PAY.getCode() && Objects.equals(
                order.getPayType(), PlusOrderPayTypeEnum.PAY_AFTER.getValue())) {
            log.info("取消前校验后付款待支付订单直接返回退款0元：{}", orderSn);
            entity.setMoneyBack(BigDecimal.ZERO);
            entity.setCanBeCancel(true);
            return entity;
        }
        //首付支付待支付订单退首付金额
        if (order.getOrderState() == PlusOrderStateEnum.WAIT_PAY.getCode() && Objects.equals(
                order.getPayType(), PlusOrderPayTypeEnum.PAY_FIRST_PERIOD.getValue())) {
            log.info("取消前校验首付支付待支付订单直接返回首付金额：{}", orderSn);
            //这里取首付金额还是当前支付金额
            entity.setMoneyBack(order.getPayAmount());
            entity.setCanBeCancel(true);
            return entity;
        }
        BigDecimal refundRate = event.getRefundRate();
        PlusCancelTypeEnum cancelType = PlusCancelTypeEnum.getByValue(event.getCancelType());
        // 对外输出传入退款比例或按比例取消,不需要计算扣减权益差价（其他取消方式没有退款比例）
        // 非代付取消才需要计算比例退款金额
        if (refundRate != null && PlusCancelTypeEnum.isNotDefrayCancel(cancelType.getValue())) {
            boolean lmk = order.getBusinessType() != null && BusinessTypeEnum.LMK.getCode()
                    .equals(order.getBusinessType());
            BigDecimal orderAmount = lmk ? order.getLmkPlusAmount() : order.getOrderAmount();
            log.info("取消校验传入退款比例：{},{},{},{}", orderSn, refundRate, orderAmount, lmk);
            BigDecimal refundAmount = orderAmount.multiply(refundRate)
                    .setScale(2, RoundingMode.HALF_DOWN);
            log.info("取消订单按比例退款金额为：{}", refundAmount);
            if (refundAmount.compareTo(orderAmount) > 0) {
                entity.setCanBeCancel(false);
                entity.setReason("退款金额超过会员价");
                entity.setCode(VipErrorEnum.PLUS_ERROR_800019.getCode());
                return entity;
            }
            // 默认可以取消
            entity.setCanBeCancel(true);
            entity.setMoneyBack(refundAmount);
            return entity;
        }
        // 计算扣减的生活权益、购物返现差价
        PlusOrderCancelEntity cancelEntity = sumDeductPrice(event, order);
        // 默认可以取消
        cancelEntity.setCanBeCancel(true);
        // 扣减差价超过会员价不可取消
        if (cancelEntity.isExceedPlusPrice()) {
            cancelEntity.setCanBeCancel(false);
            cancelEntity.setReason("扣减金额超过会员价");
            cancelEntity.setCode(VipErrorEnum.PLUS_ERROR_800015.getCode());
        }
        log.info("计算订单退款金额返回：{}", JSON.toJSONString(cancelEntity));
        return cancelEntity;
    }

    /**
     * 根据多个订单号批量获取订单数据
     */
    @Override
    public List<PlusOrderEntity> getPlusOrderBySns(List<String> plusOrderSns) {
        return plusOrderConverter.toPlusOrderEntityList(
                plusOrderInfoMapper.getOrderByOrderSns(plusOrderSns));
    }

    @Override
    public List<PlusOrderEntity> getUserWaitPayOrderList(Integer userId, Integer channelId,
            Integer programId) {
        PlusOrderQueryReq query = new PlusOrderQueryReq();

        query.setUserId(userId);
        query.setChannelId(channelId);
        query.setProgramId(programId);
        //query.setPayType(PlusOrderPayTypeEnum.PAY_AFTER.getValue());

        List<Integer> payTypes = new ArrayList<>();
        payTypes.add(PlusOrderPayTypeEnum.PAY_FIRST_PERIOD.getValue());
        payTypes.add(PlusOrderPayTypeEnum.PAY_AFTER.getValue());
        query.setPayTypes(payTypes);

        query.setOrderState(PlusOrderStateEnum.WAIT_PAY.getCode());

        List<PlusOrderInfoPo>  list = plusOrderInfoMapper.getVirtualPlusOrderInfoList(query);

        return plusOrderConverter.toPlusOrderEntityList(list);

        //return this.getUserOrderList(query);
    }

    @Override
    public List<PlusOrderEntity> getUserWaitPayOrderList(Integer userId, Integer channelId) {
//        PlusOrderQueryReq query = new PlusOrderQueryReq();
//        query.setUserId(userId);
//        query.setChannelId(channelId);
//        query.setPayType(PlusOrderPayTypeEnum.PAY_AFTER.getValue());
//        query.setOrderState(PlusOrderStateEnum.WAIT_PAY.getCode());
//        return this.getUserOrderList(query);
        List<Integer> payTypes = new ArrayList<>();
        payTypes.add(PlusOrderPayTypeEnum.PAY_FIRST_PERIOD.getValue());
        payTypes.add(PlusOrderPayTypeEnum.PAY_AFTER.getValue());
        return this.getUserWaitPayOrderListByCondition(userId, channelId, payTypes);
    }

    @Override
    public List<PlusOrderEntity> getUserWaitPayOrderListByCondition(Integer userId, Integer channelId, List<Integer> payTypes) {
        PlusOrderQueryReq query = new PlusOrderQueryReq();
        query.setUserId(userId);
        query.setChannelId(channelId);
        query.setPayTypes(payTypes);
        query.setOrderState(PlusOrderStateEnum.WAIT_PAY.getCode());
        List<PlusOrderInfoPo> list = plusOrderInfoMapper.getPlusOrderInfoListByCondition(query);
        return plusOrderConverter.toPlusOrderEntityList(list);
    }

    /**
     * 获取用户订单列表
     */
    @Override
    public List<PlusOrderEntity> getUserOrderList(PlusOrderQueryReq query) {
        PlusOrderInfoPo plusOrderInfo = new PlusOrderInfoPo();
        plusOrderInfo.setUserId(query.getUserId());
        plusOrderInfo.setChannelId(query.getChannelId());
        plusOrderInfo.setConfigId(query.getConfigId());
        plusOrderInfo.setProgramId(query.getProgramId());
        plusOrderInfo.setPayType(query.getPayType());
        plusOrderInfo.setOrderState(query.getOrderState());
        List<PlusOrderInfoPo> list = plusOrderInfoMapper.getPlusOrderInfoList(plusOrderInfo);
        return plusOrderConverter.toPlusOrderEntityList(list);
    }

    @Override
    public List<PlusOrderEntity> getUserSevenOrderList(Integer userId, Integer channelId,
            Integer... configIds) {
        List<PlusOrderInfoPo> list = plusOrderInfoMapper.getSevenPlusOrder(userId, channelId,
                Arrays.asList(configIds));
        return plusOrderConverter.toPlusOrderEntityList(list);
    }

    @Override
    public List<PlusOrderEntity> getPlusOrderByAuthTime(Integer userId, Integer channelId, Integer... configIds) {
        AuthApproval authState = authExternalRepositoryAcl.getAuthState(userId, channelId);
        List<PlusOrderInfoPo> list = plusOrderInfoMapper.getPlusOrderByAuthTime(userId, channelId,
                Arrays.asList(configIds),authState.getSuccessTime());
        return plusOrderConverter.toPlusOrderEntityList(list);
    }

    @Override
    public List<PlusOrderEntity> getWaitPayOrderList(List<String> orders) {
        List<PlusOrderInfoPo> list = plusOrderInfoMapper.getWaitAfterPayList(orders);
        return plusOrderConverter.toPlusOrderEntityList(list);
    }

    @Override
    public List<PlusOrderEntity> getOrderList(List<String> orders) {
        List<PlusOrderInfoPo> list = plusOrderInfoMapper.getOrderList(orders);
        return plusOrderConverter.toPlusOrderEntityList(list);
    }

    @Override
    public void expireCancelOrder(List<String> orderSns) {
        plusOrderInfoMapper.batchUpdateOrderStatus(orderSns);
    }

    @Override
    public List<PlusOrderEntity> getAfterPayOrderInfoList(Integer userId, List<Integer> configIds) {
        List<PlusOrderInfoPo> list = plusOrderInfoMapper.getAfterPayOrderInfoList(userId,
                configIds);
        return plusOrderConverter.toPlusOrderEntityList(list);
    }

    @Override
    public List<PlusOrderEntity> getSevenPlusOrder(Integer userId, Integer channelId,
            List<Integer> configIds) {
        log.info("获取用户七天内下过的订单开始：{}，{}", userId, configIds);
        List<PlusOrderInfoPo> sevenPlusOrder = plusOrderInfoMapper.getSevenPlusOrder(userId,
                channelId, configIds);
        log.info("用户七天内下过的订单结果：{}", JSON.toJSONString(sevenPlusOrder));
        return plusOrderConverter.toPlusOrderEntityList(sevenPlusOrder);
    }

    @Override
    public PlusOrderEntity getUserWaitPayOrder(Integer userId, Integer configId) {
        PlusOrderInfoPo po = plusOrderInfoMapper.getAfterPayOrderInfo(userId, configId);
        return plusOrderConverter.toPlusOrderEntity(po);
    }

    @Override
    public PlusOrderEntity getUserWaitPayOrderAfterAuthTime(Integer userId, Integer configId, Date authTime) {
        PlusOrderInfoPo po = plusOrderInfoMapper.getAfterPayOrderInfoAfterAuthTime(userId, configId, authTime);
        return plusOrderConverter.toPlusOrderEntity(po);
    }

    @Override
    public List<PlusOrderEntity> getPayRecordByProgram(Integer programId) {
        List<PlusOrderInfoPo> list = plusOrderInfoMapper.getPayRecordByProgram(programId);
        return plusOrderConverter.toPlusOrderEntityList(list);
    }

    @Override
    public List<PlusOrderResubmitFlagEntity> getToBeVerifyList() {
        return plusOrderConverter.toPlusOrderResubmitFlagEntityList(
                plusOrderResubmitFlagMapper.getToBeVerifyList());
    }

    @Override
    public Integer batchProcessing(List<Integer> ids) {
        return plusOrderResubmitFlagMapper.batchProcessing(ids);
    }

    @Override
    public Integer updatePlusOrderResubmitFlag(PlusOrderResubmitFlagEntity plusOrderResubmitFlag) {
        return plusOrderResubmitFlagMapper.updatePlusOrderResubmitFlag(plusOrderResubmitFlag);
    }

    @Override
    public List<PlusOrderEntity> getWaitPayOrderList(Integer userId, List<Integer> configIdList) {
        List<PlusOrderInfoPo> list = plusOrderInfoMapper.getWaitPayOrderList(userId, configIdList);
        return plusOrderConverter.toPlusOrderEntityList(list);
    }

    @Override
    public List<PlusOrderEntity> pageListByState(PlusOrderInfoQueryReq req) {
        return plusOrderInfoMapper.pageListByState(req, req.getStartPage(), req.getPageSize());
    }

    @Override
    public Integer pageListCountByState(PlusOrderInfoQueryReq req) {
        return plusOrderInfoMapper.pageListCountByState(req);
    }

    @Override
    public List<PlusOrderEntity> pageList(PlusOrderInfoQueryReq req) {
        return plusOrderInfoMapper.getPageList(req, req.getStartPage(), req.getPageSize());
    }

    @Override
    public Integer pageListCount(PlusOrderInfoQueryReq req) {
        if (req.getPlusState() != null) {
            return plusOrderInfoMapper.pageListCountByState(req);
        }
        return plusOrderInfoMapper.pageListCount(req);
    }

    /**
     * 获取7天内购买的有效会员订单信息
     */
    @Override
    public List<PlusOrderEntity> getMemberSevenOrderList(Integer userId, Integer channelId) {
        List<PlusOrderInfoPo> list = plusOrderInfoMapper.getAllSevenPlusOrder(userId, channelId);
        return plusOrderConverter.toPlusOrderEntityList(list);
    }

    @Override
    public List<PlusOrderEntity> getPlusOrderInfoListByAuthTime(Integer userId, Integer channelId, Date authTime) {
        List<PlusOrderInfoPo> list = plusOrderInfoMapper.getPlusOrderInfoListByAuthTime(userId, channelId, authTime);
        return plusOrderConverter.toPlusOrderEntityList(list);
    }

    @Override
    public List<PlusOrderEntity> getUserWaitPayOrderList(PlusOrderQueryReq req) {
        PlusOrderInfoPo plusOrderInfo = new PlusOrderInfoPo();
        plusOrderInfo.setUserId(req.getUserId());
        plusOrderInfo.setChannelId(req.getChannelId());
        plusOrderInfo.setConfigId(req.getConfigId());
        plusOrderInfo.setProgramId(req.getProgramId());
        plusOrderInfo.setPayType(PlusOrderPayTypeEnum.PAY_AFTER.getValue());
        plusOrderInfo.setOrderState(PlusOrderStateEnum.WAIT_PAY.getCode());
        List<PlusOrderInfoPo> list = plusOrderInfoMapper.getPlusOrderList(plusOrderInfo);
        return plusOrderConverter.toPlusOrderEntityList(list);
    }

    @Override
    public List<PlusOrderEntity> getPlusOrderListByIdLimit(Integer startId, Integer maxId, Integer batchSize) {
        return plusOrderInfoMapper.getPlusOrderListByIdLimit(startId, maxId, batchSize);
    }

    @Override
    public List<PlusOrderEntity> getPlusOrderByIds(List<Integer> ids) {
        return  plusOrderInfoMapper.getPlusOrderByIds(ids);
    }

    @Override
    public PlusOrderEntity getPlusOutOrder(PlusOutOrderQueryReq req) {
        return plusOrderInfoMapper.getPlusOutOrder(req);
    }

    @Override
    public void updatePlusOrderInfoByOrderSn(PlusOrderInfoPo plusOrderInfoPo) {
        plusOrderInfoMapper.updatePlusOrderInfoByOrderSn(plusOrderInfoPo);
    }


}

package com.juzifenqi.plus.module.order.api.impl;

import com.alibaba.fastjson.JSONObject;
import com.juzifenqi.plus.api.IPlusOrderJobApi;
import com.juzifenqi.plus.dto.req.OrderNoticeExecuteReq;
import com.juzifenqi.plus.dto.req.OrderRefundExecuteReq;
import com.juzifenqi.plus.dto.req.RefundExecuteReq;
import com.juzifenqi.plus.dto.resp.PlusAbyssResult;
import com.juzifenqi.plus.dubbo.DubboService;
import com.juzifenqi.plus.enums.PlusPayTypeEnum;
import com.juzifenqi.plus.enums.RenewStateEnum;
import com.juzifenqi.plus.module.asserts.application.IMemberPluSmsApplication;
import com.juzifenqi.plus.module.asserts.application.IMemberPlusCashbackApplication;
import com.juzifenqi.plus.module.asserts.application.IMemberPlusInfoApplication;
import com.juzifenqi.plus.module.asserts.application.IMemberPlusSendPlanTaskApplication;
import com.juzifenqi.plus.module.common.application.IPlusCommonAdminApplication;
import com.juzifenqi.plus.module.order.api.converter.IPlusOrderJobConverter;
import com.juzifenqi.plus.module.order.application.IPlusOrderApplication;
import com.juzifenqi.plus.module.order.application.IPlusOrderApplyApplication;
import com.juzifenqi.plus.module.order.application.IPlusOrderBatchCancelApplication;
import com.juzifenqi.plus.module.order.application.IPlusOrderBillApplication;
import com.juzifenqi.plus.module.order.application.IPlusOrderDefrayApplication;
import com.juzifenqi.plus.module.order.application.IPlusOrderNoticeApplication;
import com.juzifenqi.plus.module.order.application.IPlusOrderQueryApplication;
import com.juzifenqi.plus.module.order.application.IPlusOrderRdzxApplication;
import com.juzifenqi.plus.module.order.application.IPlusOrderSmsApplication;
import com.juzifenqi.plus.module.order.application.IPlusRefundRecordApplication;
import com.juzifenqi.plus.module.order.application.IPlusResubmitGroupApplication;
import com.juzifenqi.plus.module.order.model.MemberPlusRenewPlanQueryModel;
import com.juzifenqi.plus.module.order.model.contract.entity.MemberPlusRenewPlanEntity;
import com.juzifenqi.plus.module.order.model.event.notice.PlusOrderNoticeExecuteEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderRefundExecuteEvent;
import com.juzifenqi.plus.module.order.model.event.order.RefundExecuteEvent;
import com.juzifenqi.plus.module.program.application.IPlusProgramApplication;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */
@Slf4j
@DubboService
public class PlusOrderJobApiImpl implements IPlusOrderJobApi {

    private final IPlusOrderJobConverter converter = IPlusOrderJobConverter.instance;

    @Autowired
    private IPlusOrderApplication              plusOrderApplication;
    @Autowired
    private MemberPlusRenewPlanQueryModel      memberPlusRenewQueryModel;
    @Autowired
    private IPlusOrderApplyApplication         applyApplication;
    @Autowired
    private IPlusOrderNoticeApplication        noticeApplication;
    @Autowired
    private IPlusOrderDefrayApplication        defrayApplication;
    @Autowired
    private IPlusRefundRecordApplication       refundRecordApplication;
    @Autowired
    private IPlusOrderBatchCancelApplication   plusOrderBatchCancelApplication;
    @Autowired
    private IPlusOrderRdzxApplication          rdzxApplication;
    @Autowired
    private IMemberPlusCashbackApplication     memberPlusCashbackApplication;
    @Autowired
    private IMemberPlusInfoApplication         memberPlusInfoApplication;
    @Autowired
    private IPlusOrderBillApplication          billApplication;
    @Autowired
    private IPlusOrderSmsApplication           smsApplication;
    @Autowired
    private IMemberPlusSendPlanTaskApplication taskApplication;
    @Autowired
    private IPlusProgramApplication            programApplication;
    @Autowired
    private IPlusCommonAdminApplication        commonAdminApplication;
    @Autowired
    private IPlusOrderRdzxApplication          plusOrderRdzxApplication;
    @Autowired
    private IMemberPluSmsApplication           memberPluSmsApplication;
    @Autowired
    private IPlusResubmitGroupApplication      groupApplication;
    @Autowired
    private IPlusOrderQueryApplication         orderQueryApplication;

    /**
     * 续费创单任务
     */
    @Override
    public void renewJob(Integer limit) {
        List<MemberPlusRenewPlanEntity> renewPlans = memberPlusRenewQueryModel.listByRenewStatus(
                RenewStateEnum.TO_BE_RENEW.getCode(), limit, 1);
        plusOrderApplication.createPlusOrderByRenewPlans(renewPlans);
    }

    /**
     * 续费创单失败重试任务
     */
    @Override
    public void renewJobRetry(Integer limit, Integer maxTryCount) {
        List<MemberPlusRenewPlanEntity> renewPlans = memberPlusRenewQueryModel.listByRenewStatus(
                RenewStateEnum.FAILED.getCode(), limit, maxTryCount);
        plusOrderApplication.createPlusOrderByRenewPlans(renewPlans);
    }


    @Override
    public PlusAbyssResult orderRefundExecute(OrderRefundExecuteReq req) {
        PlusOrderRefundExecuteEvent event = converter.toPlusOrderRefundExecuteEvent(req);
        applyApplication.orderRefundExecute(event);
        return PlusAbyssResult.success();
    }

    @Override
    public PlusAbyssResult orderNoticeExecute(OrderNoticeExecuteReq req) {
        PlusOrderNoticeExecuteEvent event = converter.toPlusOrderNoticeExecuteEvent(req);
        noticeApplication.orderNoticeExecute(event);
        return PlusAbyssResult.success();
    }

    @Override
    public void orderDefrayExecute() {
        defrayApplication.orderDefrayExecute();
    }

    @Override
    public void delayRefundExecute(Integer size) {
        refundRecordApplication.delayRefundExecute(size);
    }

    @Override
    public void refundExecute(RefundExecuteReq req) {
        RefundExecuteEvent event = converter.toRefundExecuteEvent(req);
        refundRecordApplication.refundExecute(event);
    }

    @Override
    public void batchCancelOrders() {
        plusOrderBatchCancelApplication.batchCancelOrdersByJob();
    }

    @Override
    public void batchCreateRdzxOrder(Integer optStatus) {
        rdzxApplication.createOrderExecute(optStatus);
    }

    @Override
    public void invalidOrder() {
        plusOrderApplication.invalidOrder();
    }

    @Override
    public void memberExpire() {
        memberPlusInfoApplication.memberExpire();
    }

    @Override
    public void incomeRetry(Integer size) {
        billApplication.incomeRetry(size);
    }

    @Override
    public void outcomeRetry(Integer size) {
        billApplication.outcomeRetry(size);
    }

    @Override
    public void contractSignReTry(Integer size) {
        billApplication.contractSignReTry(size);
    }

    @Override
    public void contractUploadReTry(Integer size) {
        billApplication.contractUploadReTry(size);
    }

    @Override
    public void mqRecordExecute() {
        refundRecordApplication.mqRecordExecute();
    }

    @Override
    public void rdzxDeduct(PlusPayTypeEnum payType) {
        rdzxApplication.batchDeduct(payType);
    }

    @Override
    public void delayBatchDeduct() {
        plusOrderApplication.delayBatchDeduct();
    }

    @Override
    public void delayDeductSms() {
        smsApplication.delayDeductSms();
    }

    /**
     * 还款返现job
     */
    @Override
    public void hkfxMicroDefrayPayJob() {
        memberPlusCashbackApplication.hkfxMicroDefrayPayJob();
    }

    /**
     * 划扣失败预警
     */
    @Override
    public void deductError(String param) {
        JSONObject jsonObject = JSONObject.parseObject(param);
        // 查询多少分钟内的数据
        Integer beforeMinutes =
                jsonObject.getInteger("beforeMinutes") != null ? jsonObject.getInteger(
                        "beforeMinutes") : 30;
        // 达到预警总数量
        Integer warningNumber =
                jsonObject.getInteger("warningNumber") != null ? jsonObject.getInteger(
                        "warningNumber") : 50;
        orderQueryApplication.deductError(beforeMinutes, warningNumber);
    }

    @Override
    public void manualDeduct(String param) {
        if (StringUtils.hasText(param)) {
            JSONObject jsonObject = JSONObject.parseObject(param);
            String orderSn = jsonObject.getString("orderSn");
            Integer configId = jsonObject.getInteger("configId");
            Integer programId = jsonObject.getInteger("programId");
            plusOrderApplication.resetDuctPlan(orderSn, configId, programId);
        }
    }

    @Override
    public void plusProgramEditPrice() {
        programApplication.executeProgramEditPriceTask();
    }

    @Override
    public void executePlusBlackTask(Integer state, Integer size) {
        commonAdminApplication.executePlusBlackTask(state, size);
    }

    @Override
    public void handlePlusBasicProfitsCache() {
        programApplication.handlePlusBasicProfitsCache();
    }

    @Override
    public void waitPaySendSms() {
        smsApplication.waitPaySendSms();
    }

    @Override
    public void repayPlusRemindSms(String s) {
        memberPluSmsApplication.repayPlusRemindSms(s);
    }

    @Override
    public void repayPlanJob() {
        plusOrderRdzxApplication.repayPlanJob();
    }

    @Override
    public void resubmitFlagVerify() {
        plusOrderApplication.resubmitFlagVerify();
    }

    @Override
    public void resubmitGroupZpCloseRetry(Integer size) {
        groupApplication.zpCloseRetry(size);
    }

    @Override
    public void resubmitGroupRetry(Integer size, Integer index) {
        groupApplication.resubmitGroupRetry(size == null ? 20 : size, index);
    }

    @Override
    public void openStoreRetry() {
        memberPlusCashbackApplication.openStoreRetry();
    }

    @Override
    public void singlePlusExpire() {
        memberPlusInfoApplication.singlePlusExpire();
    }

    @Override
    public void xeykRenewRemind() {
        smsApplication.xeykRenewRemind();
    }

    @Override
    public void deductIncomeRetry(Integer limit) {
        billApplication.deductIncomeRetry(limit);
    }

    @Override
    public void microDefrayPayExecute() {
        memberPlusCashbackApplication.microDefrayPayExecute();
    }

    /**
     * 权益发放计划任务
     */
    @Override
    public void plusSendPlanTasks(Integer modelId) {
        taskApplication.plusSendPlanTasks(modelId);
    }

    /**
     * 移动权益发放计划任务
     */
    @Override
    public void movePlusSendPlanTasks() {
        taskApplication.movePlusSendPlanTasks();
    }

    /**
     * 原super-plus领取优惠券跑批 品牌专区+月享红包 待历史数据跑批完后可以废弃
     *
     * <AUTHOR>
     * @version 1.0
     * @date 2024/3/1 13:59
     */
    @Override
    public void oldGrantCouponJob() {
        taskApplication.oldGrantCouponJob();
    }

    /**
     * 补偿处理未签署合同的融单卡订单
     */
    @Override
    public void manualHandleRdzxOrderContractJob() {
        plusOrderRdzxApplication.manualHandleRdzxOrderContractJob();
    }

    @Override
    public void makeUpHandleRdzxOrderContractJob() {
        plusOrderRdzxApplication.makeUpHandleRdzxOrderContractJob();
    }

    @Override
    public void initPastCardNoUuid(Long index, Integer size) {
        defrayApplication.initPastCardNoUuid(index, size);
    }

    @Override
    public void inspectPastCardNoUuid(Integer size) {
        defrayApplication.inspectPastCardNoUuid(size);
    }

    @Override
    public void initAiOutboundMobileJob(Integer size) {
        smsApplication.initAiOutboundMobileJob(size);
    }

    @Override
    public void orderRetryRefund(Long refundInfoId) {
        plusOrderApplication.retryOrderSecondRefund(refundInfoId);
    }
}

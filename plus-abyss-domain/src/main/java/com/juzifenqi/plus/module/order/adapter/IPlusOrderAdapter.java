package com.juzifenqi.plus.module.order.adapter;

import com.juzifenqi.plus.module.order.adapter.event.PlusRepeatPayEvent;
import com.juzifenqi.plus.module.order.application.ao.PlusOrderDetailAo;
import com.juzifenqi.plus.module.order.model.contract.entity.OrderPayResultEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;

/**
 * 重复支付订单特殊处理
 *
 * <AUTHOR>
 * @date 2023/08/29 10:54
 **/
public interface IPlusOrderAdapter {

    /**
     * 保存重复支付订单
     */
    void saveRepeatOrder(PlusRepeatPayEvent repeatPayEvent, PlusOrderEntity plusOrderEntity);

    /**
     * 取消重复支付订单
     */
    Boolean cancelRepeatOrder(String orderSn, Integer optId, String optName);

    /**
     * 订单详情-设置订单信息
     */
    void setOrderInfoForPlusDetail(PlusOrderEntity orderEntity, PlusOrderDetailAo detailAo);

    /**
     * 订单详情-设置订单支付记录信息
     */
    void setOrderSeparateRecord(PlusOrderEntity orderEntity, PlusOrderDetailAo detailAo);
    /**
     * 订单详情-设置退款信息
     */
    void setRefundInfoForPlusDetail(PlusOrderEntity orderEntity, PlusOrderDetailAo detailAo);

    /**
     * 订单详情-设置权益信息
     */
    void setProfitInfoForPlusDetail(PlusOrderEntity orderEntity, PlusOrderDetailAo detailAo);

    /**
     * 订单详情-设置用户信息
     */
    void setMemberInfoForPlusDetail(PlusOrderEntity orderEntity, PlusOrderDetailAo detailAo);

    /**
     * 订单详情-设置融担咨询卡关联信息
     */
    void setRelationInfoForPlusDetail(PlusOrderEntity orderEntity, PlusOrderDetailAo detailAo);

    /**
     * 查询订单支付状态，兼容新老支付系统
     */
    OrderPayResultEntity queryPayRecord(String orderSn);

    /**
     * 订单详情-设置月卡续费计划信息
     */
    void setRenewalPlanInfoForPlusDetail(PlusOrderEntity orderEntity, PlusOrderDetailAo detailAo);
}

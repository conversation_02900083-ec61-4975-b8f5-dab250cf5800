package com.juzifenqi.plus.module.common;

import com.juzishuke.contract.dto.rpc.response.ContractTemplatePreviewRes;
import com.juzishuke.contract.dto.rpc.response.DownloadContractRes;
import java.util.List;

/**
 * 合同系统
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/17 17:25
 */
public interface IContractExternalRepository {

    /**
     * 获取合同信息
     */
    List<DownloadContractRes> getContract(Integer userId, String orderId);

    /**
     * 获取合同模板预览列表
     */
    List<ContractTemplatePreviewRes> queryContractTemplatePreviewList(String channelCode, Integer customerId);
}

package com.juzifenqi.plus.module.order.adapter.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.groot.utils.core.date.LocalDateTimeUtils;
import com.groot.utils.extra.SensitiveInfoUtils;
import com.juzifenqi.order.constants.OrderTypeEnum;
import com.juzifenqi.order.dao.entity.OrdersBase;
import com.juzifenqi.order.vo.OrderCancelRefundResultVO;
import com.juzifenqi.order.vo.OrderCancelRefundVO;
import com.juzifenqi.order.vo.RefundInfo;
import com.juzifenqi.plus.constants.CommonConstant;
import com.juzifenqi.plus.constants.PaySourceConstant;
import com.juzifenqi.plus.constants.RedisConstantPrefix;
import com.juzifenqi.plus.constants.SerialNoPrefixConstant;
import com.juzifenqi.plus.dto.resp.EnumResp;
import com.juzifenqi.plus.enums.*;
import com.juzifenqi.plus.enums.pay.PayProductCodeEnum;
import com.juzifenqi.plus.enums.pay.PayStateCodeEnum;
import com.juzifenqi.plus.enums.refund.RefundInfoStateEnum;
import com.juzifenqi.plus.exception.PlusAbyssException;
import com.juzifenqi.plus.module.common.IDecryptRepository;
import com.juzifenqi.plus.module.common.IFmsRepository;
import com.juzifenqi.plus.module.common.IMemberPlusSystemLogRepository;
import com.juzifenqi.plus.module.common.IPlusShuntRepository;
import com.juzifenqi.plus.module.common.entity.MemberPlusSystemLogEntity;
import com.juzifenqi.plus.module.common.entity.TraderInfoRespEntity;
import com.juzifenqi.plus.module.common.entity.UserCardNewEntity;
import com.juzifenqi.plus.module.order.adapter.IPlusOrderAdapter;
import com.juzifenqi.plus.module.order.adapter.converter.IPlusOrderAdapterConverter;
import com.juzifenqi.plus.module.order.adapter.event.PlusRepeatPayEvent;
import com.juzifenqi.plus.module.order.application.ao.PlusOrderDetailAo;
import com.juzifenqi.plus.module.order.application.ao.PlusOrderSeparateAo;
import com.juzifenqi.plus.module.order.application.ao.PlusRefundAo;
import com.juzifenqi.plus.module.order.application.ao.PlusRenewalPlanAo;
import com.juzifenqi.plus.module.order.model.IPlusOrderRdzxModel;
import com.juzifenqi.plus.module.order.model.IPlusOrderRefundInfoModel;
import com.juzifenqi.plus.module.order.model.PlusOrderDetailModel;
import com.juzifenqi.plus.module.order.model.contract.IPlusMonthMemberRenewalPlanRepository;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderRepository;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderSeparateRepository;
import com.juzifenqi.plus.module.order.model.contract.entity.OrderPayResultEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusMonthMemberRenewalPlanEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderCancelEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderDetailMemberEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderDetailOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderDetailProfitEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderDetailRefundEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusRdzxRelationDetailEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.refund.PlusOrderRefundInfoEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.separate.PlusOrderSeparateEntity;
import com.juzifenqi.plus.module.order.model.contract.external.IOrderExternalRepository;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderCancelEvent;
import com.juzifenqi.plus.module.order.repository.dao.IMemberPlusRepeatOrderMapper;
import com.juzifenqi.plus.module.order.repository.po.MemberPlusRepeatOrderPo;
import com.juzifenqi.plus.utils.RedisLock;
import com.juzifenqi.plus.utils.SerialNoUtils;
import com.juzifenqi.plus.utils.SwitchUtil;
import com.juzishuke.framework.common.util.DateUtil;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 重复支付
 *
 * <AUTHOR>
 * @date 2023/08/29 10:54
 **/
@Service
@Slf4j
public class IPlusOrderAdapterImpl implements IPlusOrderAdapter {

    @Autowired
    private IMemberPlusRepeatOrderMapper memberPlusRepeatOrderMapper;
    @Autowired
    private IOrderExternalRepository orderExternalRepository;
    @Autowired
    private IMemberPlusSystemLogRepository memberPlusSystemLogRepository;
    @Autowired
    private IPlusOrderRepository plusOrderRepository;
    @Autowired
    private RedisLock redisLock;
    @Autowired
    private PlusOrderDetailModel plusOrderDetailModel;
    @Autowired
    private IPlusOrderRdzxModel rdzxModel;
    @Autowired
    private IFmsRepository fmsRepository;
    @Autowired
    private IDecryptRepository decryptRepository;
    @Autowired
    private IPlusOrderSeparateRepository plusOrderSeparateRepository;
    @Autowired
    private IPlusOrderRefundInfoModel orderRefundInfoModel;
    @Autowired
    private IPlusShuntRepository shuntRepository;
    @Autowired
    private IOrderExternalRepository iOrderExternalRepository;
    @Autowired
    private SwitchUtil switchUtil;
    @Autowired
    private IPlusOrderAdapter   plusOrderAdapter;
    @Autowired
    private IPlusMonthMemberRenewalPlanRepository plusMonthMemberRenewalPlanRepository;

    private final IPlusOrderAdapterConverter converter = IPlusOrderAdapterConverter.instance;


    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void saveRepeatOrder(PlusRepeatPayEvent event, PlusOrderEntity plusOrderEntity) {
        try {
            log.info("保存重复支付订单入参:{}", JSONObject.toJSONString(event));
            OrdersBase ordersBase = orderExternalRepository.getByOrderSn(event.getPlusOrderSn());
            if (ordersBase == null) {
                throw new PlusAbyssException("保存重复支付订单-订单信息获取失败");
            }
            MemberPlusRepeatOrderPo memberPlusRepeatOrder = new MemberPlusRepeatOrderPo();
            memberPlusRepeatOrder.setOrderAmount(ordersBase.getMoneyOrder());
            memberPlusRepeatOrder.setOrderTime(ordersBase.getCreateTime());
            memberPlusRepeatOrder.setUserName(ordersBase.getMemberName());
            memberPlusRepeatOrder.setUserId(event.getUserId());
            memberPlusRepeatOrder.setOrderSn(event.getPlusOrderSn());
            memberPlusRepeatOrder.setChannelId(event.getChannelId());
            memberPlusRepeatOrder.setOptState(0);
            memberPlusRepeatOrder.setProgramId(event.getProgramId());
            memberPlusRepeatOrder.setProgramName(event.getProgramName());
            memberPlusRepeatOrder.setConfigId(event.getConfigId());
            memberPlusRepeatOrder.setBizSource(plusOrderEntity.getBizSource());
            int payType = CommonConstant.ONE;
            if (OrderTypeEnum.会员后付款订单.getCode().equals(ordersBase.getOrderType())) {
                payType = CommonConstant.THREE;
            }
            memberPlusRepeatOrder.setPayType(payType);
            Integer result = memberPlusRepeatOrderMapper.saveRepeatOrder(memberPlusRepeatOrder);
            log.info("保存重复支付订单结束:{}", result);
            // 判断是否为后付款订单-后付款订单直接取消
            if (OrderTypeEnum.会员后付款订单.getCode().equals(ordersBase.getOrderType())) {
                log.info("重复支付订单-后付款订单取消:{}", event.getPlusOrderSn());
                cancelRepeatOrderProcess(event.getPlusOrderSn(), CommonConstant.ZERO,
                        CommonConstant.OPERATPR_NAME);
            }
        } catch (Exception e) {
            log.info("保存重复支付订单异常", e);
        }
    }

    @Override
    @Transactional
    public Boolean cancelRepeatOrder(String orderSn, Integer optId, String optName) {
        return cancelRepeatOrderProcess(orderSn, optId, optName);
    }

    @Override
    public void setOrderInfoForPlusDetail(PlusOrderEntity orderEntity, PlusOrderDetailAo detailAo) {
        PlusOrderDetailOrderEntity entity = plusOrderDetailModel.getOrderInfo(orderEntity);
        if (Objects.isNull(entity)) {
            return;
        }
        detailAo.setOrderSn(entity.getOrderSn());
        detailAo.setOrderAmount(entity.getOrderAmount());
        detailAo.setPayAmount(entity.getPayAmount());
        detailAo.setDiscountRate(entity.getDiscountRate());
        detailAo.setConfigId(entity.getConfigId());
        detailAo.setPayState(entity.getPayState());
        //detailAo.setPayType(entity.getPayType());
        if (null != orderEntity.getPayType()) {
            detailAo.setPayType(String.valueOf(orderEntity.getPayType()));
        }
        detailAo.setPayCardNo(entity.getPayCardNo());
        detailAo.setPayTypeCode(entity.getPayTypeCode());
        detailAo.setQuotaInfo(entity.getQuotaInfo());
        detailAo.setSendQuotaInfo(entity.getSendQuotaInfo());
        detailAo.setCanBeCancel(entity.getCanBeCancel());
        //首付金额
        detailAo.setFirstPayAmount(orderEntity.getFirstPayAmount());
        //订单状态
        detailAo.setOrderState(orderEntity.getOrderState());
    }

    @Override
    public void setOrderSeparateRecord(PlusOrderEntity orderEntity, PlusOrderDetailAo detailAo) {
        List<PlusOrderSeparateEntity> orderSeparateList = plusOrderDetailModel.getOrderSeparateInfo(orderEntity);
        if (CollectionUtils.isEmpty(orderSeparateList)) {
            log.info("orderSn:{}, 查询分账记录为空", orderEntity.getOrderSn());
            return;
        }
        List<PlusOrderSeparateAo> separateAoList = Lists.newArrayList();
        for (PlusOrderSeparateEntity separateEntity : orderSeparateList) {
            PlusOrderSeparateAo separateAo = new PlusOrderSeparateAo();
            separateAo.setId(separateEntity.getId());
            separateAo.setOrderSn(separateEntity.getOrderSn());
            separateAo.setAmount(separateEntity.getTotalSeparateAmount());
            separateAo.setApplySerialNo(separateEntity.getApplySerialNo());
            separateAo.setPaySerialNo(separateEntity.getPaySerialNo());
            separateAo.setCreateTime(LocalDateTimeUtils.formatDateToStr(separateEntity.getCreateTime()));
            if (separateEntity.getPayCallbackTime() != null) {
                separateAo.setPayCallbackTime(LocalDateTimeUtils.formatDateToStr(separateEntity.getPayCallbackTime()));
            }
            separateAo.setPayResult(buildPayResult(separateEntity.getSeparateState()));
            separateAo.setPayAction(buildPayAction(separateEntity.getOrderPayAction()));
            separateAo.setRemark(separateEntity.getRemark());
            separateAo.setSeparateFlag(separateEntity.getSeparateEnableState());
            separateAo.setSettleFlag(separateEntity.getSettleEnableState());

            TraderInfoRespEntity traderInfoRespEntity = null;
            try{
                if (Objects.equals(separateEntity.getOrderPayAction(), OrderPayActionEnum.PAY_ACTION_1.getCode())) {
                    traderInfoRespEntity = fmsRepository.queryCashierResult(separateEntity.getApplySerialNo());
                } else {
                    traderInfoRespEntity = fmsRepository.queryPayRecordV2(separateEntity.getApplySerialNo());
                }
            }catch (PlusAbyssException e){
                log.error("查询收银台支付状态,获取结果失败",e);
            }
            if (!Objects.isNull(traderInfoRespEntity)) {
                PayProductCodeEnum payProductCodeEnum = PayProductCodeEnum.getTypeByCode(
                        traderInfoRespEntity.getPayProductCode());
                if (payProductCodeEnum != null) {
                    separateAo.setPayWay(payProductCodeEnum.getDesc());
                }
            }
            separateAoList.add(separateAo);
        }
        detailAo.setSeparateList(separateAoList);
    }

    private EnumResp buildPayResult(Integer separateState) {
        if (Objects.equals(separateState, SeparateStateEnum.PROCESSING.getCode())) {
            return new EnumResp(separateState, "支付中");
        }
        if (Objects.equals(separateState, SeparateStateEnum.FAIL.getCode())) {
            return new EnumResp(separateState, "支付失败");
        }
        if (Objects.equals(separateState, SeparateStateEnum.SUCCESS.getCode())) {
            return new EnumResp(separateState, "支付成功");
        }
        return null;
    }

    private EnumResp buildPayAction(Integer payAction) {
        if (Objects.equals(payAction, OrderPayActionEnum.PAY_ACTION_1.getCode())) {
            return new EnumResp(payAction, OrderPayActionEnum.PAY_ACTION_1.getName());
        }
        if (Objects.equals(payAction, OrderPayActionEnum.PAY_ACTION_2.getCode())) {
            return new EnumResp(payAction, OrderPayActionEnum.PAY_ACTION_2.getName());
        }
        return null;
    }

    @Override
    public void setRefundInfoForPlusDetail(PlusOrderEntity orderEntity,
                                           PlusOrderDetailAo detailAo) {
        List<PlusRefundAo>  refundList = new ArrayList<>();
        if (!PlusOrderPayTypeEnum.PAY_FIRST_PERIOD.getValue().equals(orderEntity.getPayType())) {
            PlusOrderDetailRefundEntity entity = plusOrderDetailModel.getRefundInfo(orderEntity);
            if (Objects.isNull(entity)) {
                return;
            }
            PlusRefundAo plusRefundAo = getPlusRefundAo(entity);
            refundList.add(plusRefundAo);
        } else {
            List<PlusOrderDetailRefundEntity> list = plusOrderDetailModel.getFirstRefundList(orderEntity);
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            for (PlusOrderDetailRefundEntity refundEntity : list) {
                PlusRefundAo plusRefundAo = getPlusRefundAo(refundEntity);
                refundList.add(plusRefundAo);
            }
        }
        detailAo.setRefundList(refundList);
    }

    private static PlusRefundAo getPlusRefundAo(PlusOrderDetailRefundEntity entity) {
        PlusRefundAo plusRefundAo = new PlusRefundAo();
        //是否扣减生活权益差价
        plusRefundAo.setNeedDeductPrice(entity.getNeedDeductPrice());
        //权益数量
        plusRefundAo.setProfitNum(entity.getProfitNum());
        //差价
        plusRefundAo.setDeductPrice(entity.getDeductPrice());
        //退款金额
        plusRefundAo.setRefundAmount(entity.getRefundAmount());
        //退款比例
        plusRefundAo.setRefundRatio(entity.getRefundRatio());
        //出账方(实际出账方)
        plusRefundAo.setOutSupplier(entity.getOutSupplier());
        //计划入账方
        plusRefundAo.setPlanInSupplier(entity.getPlanInSupplier());
        //实际入账方
        plusRefundAo.setInSupplier(entity.getInSupplier());
        //退款方式：0 原路退 1 代付
        plusRefundAo.setRefundType(entity.getRefundType());
        //退款状态: 0_待处理 1_退款中 2_退款成功 3_退款失败 4_存疑
        plusRefundAo.setRefundState(entity.getRefundState());
        //退款失败原因
        plusRefundAo.setRefundFailMsg(entity.getRefundFailMsg());
        //退款流水号
        plusRefundAo.setRefundSerialNumber(entity.getRefundSerialNumber());
        //打款银行名称
        plusRefundAo.setRefundBankName(entity.getRefundBankName());
        //退款通道
        plusRefundAo.setRefundChannel(entity.getRefundChannel());
        //退款银行信息（银行卡后四位）
        plusRefundAo.setRefundBankCardNo(entity.getRefundBankCardNo());
        //更新时间
        plusRefundAo.setUpdateTime(entity.getUpdateTime());
        //退费时间
        plusRefundAo.setRefundTime(entity.getRefundTime());
        return plusRefundAo;
    }

    @Override
    public void setProfitInfoForPlusDetail(PlusOrderEntity orderEntity,
                                           PlusOrderDetailAo detailAo) {
        PlusOrderDetailProfitEntity entity = plusOrderDetailModel.getProfitInfo(orderEntity);
        if (Objects.isNull(entity)) {
            return;
        }
        detailAo.setDetailId(entity.getDetailId());
        detailAo.setPlusState(entity.getPlusState());
        detailAo.setEffectiveNum(entity.getEffectiveNum());
        detailAo.setCurrentStartTime(entity.getCurrentStartTime());
        detailAo.setCurrentEndTime(entity.getCurrentEndTime());
        detailAo.setProfitList(entity.getProfitList());
    }

    @Override
    public void setMemberInfoForPlusDetail(PlusOrderEntity orderEntity,
                                           PlusOrderDetailAo detailAo) {
        PlusOrderDetailMemberEntity entity = plusOrderDetailModel.getMemberInfo(orderEntity);
        if (Objects.isNull(entity)) {
            return;
        }
        detailAo.setUserId(entity.getUserId());
        detailAo.setUserName(entity.getUserName());
        detailAo.setMobile(entity.getMobile());
        detailAo.setChannelId(entity.getChannelId());
        detailAo.setEndTime(entity.getEndTime());
        detailAo.setStartTime(entity.getStartTime());
        detailAo.setAllPlusType(entity.getAllPlusType());
    }

    @Override
    public void setRelationInfoForPlusDetail(PlusOrderEntity orderEntity,
                                             PlusOrderDetailAo detailAo) {
        if (orderEntity.getConfigId() != JuziPlusEnum.RDZX_CARD.getCode()) {
            return;
        }
        PlusRdzxRelationDetailEntity relationDetail = rdzxModel.getRelationDetail(
                orderEntity.getOrderSn(), orderEntity.getUserId());
        if (relationDetail == null) {
            return;
        }
        detailAo.setLoanOrderSn(relationDetail.getOrderSn());
        detailAo.setDeductionTime(DateUtil.formatDate(relationDetail.getPlanTime()));
        UserCardNewEntity cardInfo = fmsRepository.getBankByIdV2(orderEntity.getUserId(),
                relationDetail.getBankId(), String.valueOf(orderEntity.getChannelId()));
        if (cardInfo != null) {
            detailAo.setBankCardNo(SensitiveInfoUtils.bankCard(
                    decryptRepository.decrypt(cardInfo.getCardNoDes(), "会员订单详情")));
        }
        if (relationDetail.getPlanTime() != null
                && DateUtil.compareDate(DateUtil.getDate(), relationDetail.getPlanTime()) > 0) {
            int daysBetween = DateUtil.getDaysBetween(relationDetail.getPlanTime(),
                    DateUtil.getDate());
            detailAo.setOverdueDays(String.valueOf(daysBetween));
        }
    }

    /**
     * 查询订单支付状态
     * 1. 主动支付查询收银台
     * 2. 代扣查询新支付
     */
    @Override
    public OrderPayResultEntity queryPayRecord(String orderSn) {
        if (StringUtils.isBlank(orderSn)) {
            throw new PlusAbyssException("查询订单支付状态,订单号不能为空");
        }
        PlusOrderSeparateEntity separateEntity = plusOrderSeparateRepository.getLastByOrderSn(orderSn);
        if (separateEntity == null) {
            log.info("查询订单支付状态,未查询到分账记录,orderSn:{}", orderSn);
            return null;
        }
        if (StringUtils.isBlank(separateEntity.getApplySerialNo())) {
            log.info("查询订单支付状态,未获取到请求支付流水号,orderSn:{}", orderSn);
            throw new PlusAbyssException("查询订单支付状态,未获取到请求支付流水号");
        }
        TraderInfoRespEntity traderInfoRespEntity;
        if (Objects.equals(separateEntity.getOrderPayAction(), OrderPayActionEnum.PAY_ACTION_1.getCode())) {
            traderInfoRespEntity = fmsRepository.queryCashierResult(separateEntity.getApplySerialNo());
        } else {
            traderInfoRespEntity = fmsRepository.queryPayRecordV2(separateEntity.getApplySerialNo());
        }
        log.info("查询订单支付状态,新支付信息查询支付状态成功,orderSn:{}, applySerialNo:{}",
                orderSn, separateEntity.getApplySerialNo());
        return new OrderPayResultEntity(OrderPayResultEntity.RESULT_TYPE_NEW, traderInfoRespEntity);
    }

    @Override
    public void setRenewalPlanInfoForPlusDetail(PlusOrderEntity orderEntity, PlusOrderDetailAo detailAo) {
        if (orderEntity.getConfigId() != JuziPlusEnum.HYYK_CARD.getCode()) {
            return;
        }
        List<PlusMonthMemberRenewalPlanEntity> renewalPlanEntities =
                plusMonthMemberRenewalPlanRepository.getByOrderSn(orderEntity.getOrderSn());
        detailAo.setRenewalPlanList(converter.toRenewalPlanAoList(renewalPlanEntities));
    }

    /**
     * 重复支付-只取消订单+退款
     *
     * @param orderSn 单号
     * @param optId   操作id
     * @param optName 操作人
     */
    public Boolean cancelRepeatOrderProcess(String orderSn, Integer optId, String optName) {
        log.info("取消重复支付订单-参数：orderSn：{}，optId：{}，optName：{}", orderSn, optId, optName);
        String key = RedisConstantPrefix.CANCEL_REPEAT_ORDER + orderSn;
        boolean lock = redisLock.lock(key, "cancelRepeatOrder", 5);
        if (!lock) {
            log.info("取消重复支付订单-操作过快：orderSn：{}", orderSn);
            return false;
        }
        //查询是否可以取消
        MemberPlusRepeatOrderPo repeatInfo = memberPlusRepeatOrderMapper.getByOrderSnAndState(
                orderSn);
        if (repeatInfo == null) {
            log.info("取消重复支付订单-未查到订单信息：orderSn：{}", orderSn);
            return false;
        }
        if (!switchUtil.isNew(repeatInfo.getUserId())) {
            log.info("重复支付订单取消非白名单用户走原逻辑：{}", orderSn);
            // 后续需要取消执行计划，需要用到支付类型 payType
            PlusOrderEntity plusOrderEntity = new PlusOrderEntity();
            plusOrderEntity.setPayType(repeatInfo.getPayType());
            //更新状态为已处理
            memberPlusRepeatOrderMapper.setInfoHandledById(repeatInfo.getId());
            // 取消订单
            PlusOrderCancelEvent cancelEvent = converter.toCancelEvent(repeatInfo);
            cancelEvent.setCancelType(PlusCancelTypeEnum.RE_PAY_CANCEL.getValue());
            cancelEvent.setPlusOrderSn(orderSn);
            PlusOrderCancelEntity cancelEntity = new PlusOrderCancelEntity();
            cancelEntity.setMoneyBack(repeatInfo.getOrderAmount());
            plusOrderRepository.cancelOrder(cancelEvent, cancelEntity);
            // 取消相关计划
            plusOrderRepository.cancelPlan(cancelEvent, plusOrderEntity);
            //调订单服务更改订单状态
            orderExternalRepository.cancelOrderForCusys(orderSn,
                    CancelReasonEnum.CANCEL_REASON_18.getName(), optName);
        } else {
            log.info("重复支付订单取消新逻辑：{}", orderSn);
            // 退款记录校验
            List<PlusOrderRefundInfoEntity> refundInfoList = orderRefundInfoModel.getByOrderSn(
                    orderSn, Arrays.asList(RefundInfoStateEnum.DOING.getCode(),
                            RefundInfoStateEnum.SUCCESS.getCode()));
            if (CollectionUtils.isNotEmpty(refundInfoList)) {
                throw new PlusAbyssException(
                        "取消重复支付订单，订单存在已退款/退款中的退款记录,不可再次操作");
            }
            // 退款业务流水号
            String refundSerialNo = SerialNoUtils.generateApplySerialNo(
                    SerialNoPrefixConstant.SERIAL_NO_PREFIX_YLT, orderSn);
            // 保存订单退款申请信息
            PlusOrderRefundInfoEntity refundInfoEntity = converter.toPlusOrderRefundInfoEntity(
                    orderSn, false, true, refundSerialNo, RefundInfoStateEnum.DOING.getCode(),
                    RefundTypeEnum.YLT.getCode(), PlusCancelTypeEnum.RE_PAY_CANCEL.getValue(),
                    optId, optName, CancelReasonEnum.CANCEL_REASON_18.getCode());
            Long id = orderRefundInfoModel.saveOrderRefundInfo(refundInfoEntity);
            // 调订单中心取消订单
            OrderCancelRefundVO vo = buildOrderCancelParam(orderSn, repeatInfo.getOrderAmount(),
                    repeatInfo.getOrderAmount(), refundSerialNo, optId, optName,
                    repeatInfo.getChannelId());
            OrderCancelRefundResultVO result = iOrderExternalRepository.closeOrderRefund(vo);
            if (result == null) {
                throw new PlusAbyssException("重复支付订单取消,调订单中心取消订单异常");
            }
            // 调订单中心取消同步返回失败,修改订单退款信息状态
            if (PayStateCodeEnum.F.getCode().equals(result.getStatus())) {
                orderRefundInfoModel.updateRefundState(refundSerialNo,
                        RefundInfoStateEnum.FAIL.getCode());
            }
            // 更新支付退款流水号,调订单可能不会同步返回支付侧流水号
            String paySerialNo = result.getSerialNumber();
            if (StringUtils.isNotBlank(paySerialNo)) {
                orderRefundInfoModel.updatePaySerialNo(id, result.getSerialNumber());
            }
        }
        //取消会员记录日志
        MemberPlusSystemLogEntity plusLog = new MemberPlusSystemLogEntity();
        plusLog.memberId(repeatInfo.getUserId()).channelId(repeatInfo.getChannelId())
                .programId(repeatInfo.getProgramId()).orderSn(orderSn)
                .cancelRemark("重复支付订单取消")
                .reason(CancelReasonEnum.CANCEL_REASON_18.getCode()).optId(optId).optName(optName);
        memberPlusSystemLogRepository.saveCancelLogByUser(plusLog, LogNodeEnum.LOG_NODE_CANCEL);
        return true;
    }

    /**
     * 构建调订单中心取消订单参数
     */
    private OrderCancelRefundVO buildOrderCancelParam(String plusOrderSn, BigDecimal moneyBack,
                                                      BigDecimal orderAmount, String refundSerialNo, Integer optId, String optName,
                                                      Integer channelId) {
        OrderCancelRefundVO vo = new OrderCancelRefundVO();
        vo.setOrderSn(plusOrderSn);
        vo.setCancelReason(CancelReasonEnum.CANCEL_REASON_18.getName());
        vo.setOperatingId(optId);
        vo.setOperatingName(optName);
        // 退款信息
        RefundInfo refundInfo = new RefundInfo();
        refundInfo.setRefundType(PayRefundTypeEnum.ORIGINAL.getCode());
        // 是否部分退款
        int partRefund =
                moneyBack.compareTo(BigDecimal.ZERO) > 0 && moneyBack.compareTo(orderAmount) < 0 ? 1
                        : 0;
        refundInfo.setPartRefund(partRefund);
        refundInfo.setApplication(String.valueOf(channelId));
        refundInfo.setSource(PaySourceConstant.PAY_SOURCE_MEMBER);
        refundInfo.setRefundAmount(moneyBack);
        refundInfo.setThirdPayNum(refundSerialNo);
        refundInfo.setPayProductCode(PayProductCodeEnum.TK.getCode());
        // 业务场景
        String businessScene = shuntRepository.getBusinessScene(plusOrderSn);
        refundInfo.setBusinessScene(businessScene);
        // 扩展信息
        // refundInfo.setExtInfo(JSONObject.toJSONString(event.getExtInfo()));
        vo.setRefundInfo(refundInfo);
        return vo;
    }
}

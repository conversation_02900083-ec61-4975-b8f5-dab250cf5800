package com.juzifenqi.plus.module.order.repository.dao;

import com.juzifenqi.plus.dto.req.admin.PlusOrderInfoQueryReq;
import com.juzifenqi.plus.dto.req.order.PlusOrderQueryReq;
import com.juzifenqi.plus.dto.req.order.PlusOutOrderQueryReq;
import com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoDetailExtPo;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.repository.po.PlusOrderInfoPo;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface IPlusOrderInfoMapper {

    /**
     * 新增
     */
    Integer insertPlusOrderInfo(@Param("plusOrderInfo") PlusOrderInfoPo plusOrderInfo);

    /**
     * 删除
     */
    Integer deletePlusOrderInfo(@Param("id") Integer id);

    /**
     * 更新
     */
    Integer updatePlusOrderInfo(@Param("plusOrderInfo") PlusOrderInfoPo plusOrderInfo);

    /**
     * 更新
     */

    Integer updatePlusOrderInfoByOrderSn(@Param("plusOrderInfo") PlusOrderInfoPo plusOrderInfo);

    /**
     * 更新
     */
    Integer updatePlusOrderInfoStatus(@Param("plusOrderInfo") PlusOrderInfoPo plusOrderInfo);

    /**
     * Load查询
     */
    PlusOrderInfoPo loadPlusOrderInfo(@Param("id") Integer id);

    PlusOrderInfoPo getByOrderSn(@Param("orderSn") String orderSn);

    List<PlusOrderInfoPo> getOrderByOrderSns(@Param("orderSns") List<String> orderSns);

    List<PlusOrderInfoPo> getPlusOrderInfoList(
            @Param("plusOrderInfo") PlusOrderInfoPo plusOrderInfo);

    List<PlusOrderInfoPo> getPlusOrderList(@Param("plusOrderInfo") PlusOrderInfoPo plusOrderInfo);

    Integer batchUpdateOrderStatus(@Param("orderSns") List<String> orderSns);

    /**
     * 分页查询Count-带状态
     */
    Integer pageListCountByState(@Param("plusOrderInfo") PlusOrderInfoQueryReq req);

    /**
     * 分页查询Count
     */
    Integer pageListCount(@Param("plusOrderInfo") PlusOrderInfoQueryReq req);

    /**
     * 分页查询
     */
    List<PlusOrderEntity> getPageList(@Param("plusOrderInfo") PlusOrderInfoQueryReq req,
            @Param("pageStart") Integer startPage, @Param("pageSize") Integer pageSize);

    /**
     * 分页查询。查生效或过期会员join-带状态
     */
    List<PlusOrderEntity> pageListByState(
            @Param("plusOrderInfo") PlusOrderInfoQueryReq req,
            @Param("pageStart") Integer startPage, @Param("pageSize") Integer pageSize);


    List<PlusOrderInfoPo> getConfigIdByUserId(@Param("userId") Integer userId);

    List<PlusOrderInfoPo> getMemberPlusOrderInfoList(
            @Param("plusOrderInfo") PlusOrderInfoPo plusOrderInfo);

    /**
     * 获取会员未过期 & 未支付 & 后付款信息
     */
    PlusOrderInfoPo getAfterPayOrderInfo(@Param("userId") Integer userId,
            @Param("configId") Integer configId);

    /**
     * 获取认证成功后未过期 & 未支付 & 后付款会员
     */
    PlusOrderInfoPo getAfterPayOrderInfoAfterAuthTime(@Param("userId") Integer userId, @Param("configId") Integer configId, @Param("authTime") Date authTime);

    /**
     * 获取会员未过期 & 未支付 & 后付款信息
     */
    List<PlusOrderInfoPo> getAfterPayOrderInfoList(@Param("userId") Integer userId,
            @Param("configIds") List<Integer> configIds);

    /**
     * 获取待支付的订单列表
     */
    List<PlusOrderInfoPo> getWaitPayOrderList(@Param("userId") Integer userId,
            @Param("configIds") List<Integer> configIds);

    /**
     * 查询用户七天内开通的会员订单信息
     *
     * @param userId 用户id
     * @param channel 渠道
     * @param idList 会员类型集合
     */
    List<PlusOrderInfoPo> getSevenPlusOrder(@Param("userId") Integer userId,
            @Param("channel") Integer channel, @Param("ids") List<Integer> idList);

    /**
     * 查询用户七天内开通的会员订单信息
     *
     * @param userId 用户id
     * @param channel 渠道
     * @param idList 会员类型集合
     */
    List<PlusOrderInfoPo> getPlusOrderByAuthTime(@Param("userId") Integer userId, @Param("channel") Integer channel,
                                                 @Param("ids") List<Integer> idList, @Param("authTime") Date authTime);

    /**
     * 查询用户所有七天内开通的会员订单信息--精确到时分秒
     *
     * @param userId 用户id
     * @param channel 渠道
     */
    List<PlusOrderInfoPo> getAllSevenPlusOrder(@Param("userId") Integer userId,
            @Param("channel") Integer channel);

    /**
     * 查询开通时间大于认证成功时间的订单
     *
     * @param userId   用户id
     * @param channel  渠道
     * @param authTime 认证成功时间
     */
    List<PlusOrderInfoPo> getPlusOrderInfoListByAuthTime(@Param("userId") Integer userId,
                                                         @Param("channel") Integer channel,
                                                         @Param("authTime") Date authTime);

    /**
     * 根据用户id和会员单号获取订单信息
     */
    PlusOrderInfoPo getInfoByOrderSn(@Param("orderSn") String orderSn,
            @Param("userId") Integer userId);

    /**
     * 查询最新的40条支付成功订单列表
     */
    List<PlusOrderInfoPo> getPayRecordByProgram(@Param("programId") Integer programId);


    /**
     * 判断是否有没有买过失败卡，失败卡只能买一次
     */
    PlusOrderInfoPo checkUserBuy(@Param("configId") Integer configId,
            @Param("userId") Integer userId, @Param("channel") Integer channel);

    /**
     * 当前用户、当前方案、待支付的数据
     */
    PlusOrderInfoPo checkRepeatOrder(@Param("userId") Integer userId,
            @Param("programId") Integer programId);

    /**
     * 获取购买过的记录数量
     */
    Integer getNumByUserAndProId(Integer memberId, Integer programId);

    /**
     * 批量修改会员周期
     */
    int updateBatchPeriods(@Param("list") List<MemberPlusInfoDetailExtPo> list);

    /**
     * 计算订单指定状态的数量
     */
    Integer countByOrderListAndState(@Param("orderList") List<String> orderList,
            @Param("orderState") Integer orderState);

    /**
     * 计算订单指定状态的订单支付金额
     */
    BigDecimal sumPayAmountByOrderListAndState(@Param("orderList") List<String> orderList,
            @Param("orderState") Integer orderState);

    /**
     * 按订单号获取待支付的后付款订单
     */
    List<PlusOrderInfoPo> getWaitAfterPayList(@Param("orders") List<String> orders);

    /**
     * 按订单号获取订单
     */
    List<PlusOrderInfoPo> getOrderList(@Param("orders") List<String> orders);

    /**
     * 根据id查询符合条件的记录
     * @return
     */
    List<PlusOrderEntity> getPlusOrderListByIdLimit(@Param("startId") Integer startId, @Param("maxId") Integer maxId,
                                                    @Param("batchSize") Integer batchSize);

    /**
     * 根据id查询记录列表
     * @param ids
     * @return
     */
    List<PlusOrderEntity> getPlusOrderByIds(@Param("ids") List<Integer> ids);

    /**
     * 根据订单号/外部订单号查询用户订单信息
     */
    PlusOrderEntity getPlusOutOrder(@Param("outOrderReq") PlusOutOrderQueryReq req);

    List<PlusOrderInfoPo> getPlusOrderInfoListByCondition(
            @Param("plusOrderQueryReq") PlusOrderQueryReq plusOrderQueryReq);

    List<PlusOrderInfoPo> getVirtualPlusOrderInfoList(
            @Param("plusOrderQueryReq") PlusOrderQueryReq plusOrderQueryReq);


}

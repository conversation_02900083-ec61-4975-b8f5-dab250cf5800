package com.juzifenqi.plus.module.order.model.impl.strategy;

import com.alibaba.fastjson.JSON;
import com.groot.utils.exception.LogUtil;
import com.juzifenqi.enumeration.OrderChannelEnum;
import com.juzifenqi.plus.constants.RedisConstantPrefix;
import com.juzifenqi.plus.enums.DeductResultStateEnum;
import com.juzifenqi.plus.enums.DeductTypeEnum;
import com.juzifenqi.plus.enums.PayStateEnum;
import com.juzifenqi.plus.enums.PlusCancelTypeEnum;
import com.juzifenqi.plus.enums.PlusDeductLogTypeEnum;
import com.juzifenqi.plus.enums.PlusPayTypeEnum;
import com.juzifenqi.plus.enums.PlusSwitchEnum;
import com.juzifenqi.plus.enums.SwitchStateEnum;
import com.juzifenqi.plus.enums.pay.PayStateCodeEnum;
import com.juzifenqi.plus.enums.supplier.SupplierTypeEnum;
import com.juzifenqi.plus.exception.PlusAbyssException;
import com.juzifenqi.plus.module.common.entity.MemberPlusSwitchControlEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOpenResultEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderDeductResEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.event.PlusDeductEvent;
import com.juzifenqi.plus.module.order.model.event.PlusPayDeductEvent;
import com.juzifenqi.plus.module.order.model.event.PlusRealDeductEvent;
import com.juzifenqi.plus.module.order.model.event.order.CreateRefundRecordEvent;
import com.juzifenqi.plus.module.order.model.event.order.LoanOrderCloseEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderCreateEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderDeductCallBackEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderPayDeductRespEvent;
import com.juzifenqi.plus.module.order.repository.po.PlusOrderShuntPo;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 会员月卡处理器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/08/15 17:00
 */
@Slf4j
@Component
public class YkPlusHandler extends AbstractStrategyHandler {
    @Override
    public void createOrderAfter(PlusOrderEntity plusOrderEntity,
                                 PlusOrderCreateEvent createEvent) {
        orderShuntRepository.saveOrderShunt(plusOrderEntity, createEvent);
        // 占用分流配置总金额和总订单笔数
        if (createEvent.getCreateOrderContext() != null) {
            Integer supplierId = createEvent.getCreateOrderContext().getShuntSupplierId();
            orderShuntRepository.addPlanShuntOrderAmount(
                    supplierId, plusOrderEntity.getOrderAmount(),
                    plusOrderEntity.getOrderSn());

            // 占用计划分流主体渠道业务场景当日金额
            String businessScene = redisUtils.get(String.format(RedisConstantPrefix.PLUS_SHUNT_SCENE, plusOrderEntity.getUserId(), supplierId));
            orderShuntRepository.addPlanShuntChannelOrderAmount(
                    supplierId,
                    plusOrderEntity.getChannelId(),
                    businessScene,
                    plusOrderEntity.getOrderAmount(),
                    plusOrderEntity.getOrderSn());
        }
    }

    @Override
    public void preProcessorAfterPlus(PlusOrderCreateEvent event, PlusProgramEntity program) {
        log.info("会员月卡后付款开通前处理开始：{}", event.getUserId());
        boolean support = programQueryModel.supportAfterPay(program.getProgramId(),
                event.getUserId(), event.getChannelId());
        if (!support) {
            throw new PlusAbyssException("当前用户不支持后付款开通");
        }
    }

    @Override
    public PlusOpenResultEntity postProcessorAfterPlus(PlusOrderCreateEvent event,
                                                       PlusProgramEntity program, PlusOrderEntity order) {
        log.info("会员月卡后付款开通后处理开始：{},{}", event.getUserId(), order.getOrderSn());
        if (event.getMonthPeriod() != 1) {
            return null;
        }
        // 保存延迟退款记录
        CreateRefundRecordEvent refundRecordEvent = converter.toCreateRefundRecordEvent(order,
                PlusCancelTypeEnum.DELAY.getValue(),
                PlusSwitchEnum.YK_REFUND.getCode());
        refundRecordModel.createDelayPlusRefundRecord(refundRecordEvent);
//        // 占用分流配置总金额和总订单笔数
//        if (event.getCreateOrderContext() != null) {
//            orderShuntRepository.addPlanShuntOrderAmount(
//                    event.getCreateOrderContext().getShuntSupplierId(), order.getOrderAmount(),
//                    order.getOrderSn());
//        }
        return null;
    }

    @Override
    public void loanFkClose(LoanOrderCloseEvent event) {
        log.info("会员月卡风控闭单处理开始：{}", JSON.toJSONString(event));
        createRapidPlusRefundRecord(event, PlusSwitchEnum.YK_REFUND.getCode());
        log.info("会员月卡风控闭单处理完成：{},{}", event.getUserId(), event.getLoanOrderSn());
    }

    @Override
    public void loanZpClose(LoanOrderCloseEvent event) {
        log.info("会员月卡资匹闭单处理开始：{}", JSON.toJSONString(event));
        createRapidPlusRefundRecord(event, PlusSwitchEnum.YK_REFUND.getCode());
        log.info("会员月卡资匹闭单处理完成：{},{}", event.getUserId(), event.getLoanOrderSn());
    }

    @Override
    public void loanClose(LoanOrderCloseEvent event) {
        log.info("会员月卡取消订单处理开始：{}", JSON.toJSONString(event));
        createRapidPlusRefundRecord(event, PlusSwitchEnum.YK_REFUND.getCode());
        log.info("会员月卡取消订单处理完成：{},{}", event.getUserId(), event.getLoanOrderSn());
    }

    @Override
    public void activeDeductPreHandle(PlusDeductEvent event) {
        log.info("会员月卡划扣前置处理开始：{}", JSON.toJSONString(event));
        Integer userId = event.getUserId();
        switch (event.getDeductFlag()) {
            case PAY_TYPE_1:
                log.info("会员月卡立即划扣前置处理开始：{}", event.getOrderSn());
                Integer orderChannelId = event.getOrderChannelId();
                // 订单渠道判断
                if (!Objects.equals(orderChannelId, OrderChannelEnum.现金贷.getChannelCode())
                        && !Objects.equals(orderChannelId,
                        OrderChannelEnum.商品订单.getChannelCode())) {
                    throw new PlusAbyssException(
                            String.valueOf(PlusDeductLogTypeEnum.LOG_TYPE_24.getCode()),
                            "非商城现金贷或商品贷,不处理立即划扣");
                }
                // 开关校验
                MemberPlusSwitchControlEntity switchControl = switchControlRepository.getSwitchByCode(
                        PlusSwitchEnum.YK_DEDUCT.getCode());
                if (switchControl == null || Objects.equals(switchControl.getStatus(),
                        SwitchStateEnum.CLOSE.getCode())) {
                    throw new PlusAbyssException(
                            String.valueOf(PlusDeductLogTypeEnum.LOG_TYPE_6.getCode()),
                            "划扣开关未开启,不处理立即划扣");
                }
                event.setSwitchControl(switchControl);
                // 获取待支付的后付款加速卡订单
                PlusOrderEntity order = orderQueryModel.getUserWaitPayOrder(userId,
                        event.getConfigId());
                log.info("会员月卡立即划扣获取用户待支付后付款订单信息：{},{}", userId,
                        JSON.toJSONString(order));
                if (order == null) {
                    throw new PlusAbyssException(
                            String.valueOf(PlusDeductLogTypeEnum.LOG_TYPE_15.getCode()),
                            "当前不存在会员月卡待支付后付款订单,不处理立即划扣");
                }
                // 当前借款订单是否是开会员卡后提交的订单
                if (order.getCreateTime().after(event.getCreateOrderTime())) {
                    throw new PlusAbyssException(
                            String.valueOf(PlusDeductLogTypeEnum.LOG_TYPE_9.getCode()),
                            "会员订单创单时间在借款单创单时间之后,不立即处理划扣");
                }
                event.setPlusOrderEntity(order);
                log.info("会员月卡立即划扣前置处理完成：{},{}", userId, event.getOrderSn());
                break;
            case PAY_TYPE_7:
                log.info("会员月卡延迟划扣前置处理开始：{}", event.getOrderSn());
                PlusOrderEntity waitPayOrder = orderQueryModel.getUserWaitPayOrder(userId,
                        event.getConfigId());
                log.info("会员月卡延迟划扣获取用户待支付后付款订单信息：{},{}", userId,
                        JSON.toJSONString(waitPayOrder));
                if (waitPayOrder == null) {
                    throw new PlusAbyssException(
                            String.valueOf(PlusDeductLogTypeEnum.LOG_TYPE_15.getCode()),
                            "当前不存在会员月卡待支付后付款订单,不处延迟即划扣");
                }
                event.setPlusOrderEntity(waitPayOrder);
                log.info("会员月卡延迟划扣前置处理完成：{},{}", userId, event.getOrderSn());
                break;
            default:
                log.info("会员月卡划扣前置处理非立即和延迟划扣类型,不处理：{}", event.getOrderSn());
                break;
        }
    }

    @Override
    public PlusOrderDeductResEntity deduct(PlusDeductEvent deductPlan) {
        log.info("会员月卡划扣申请开始：{}", JSON.toJSONString(deductPlan));
        Integer userId = deductPlan.getUserId();
        PlusOrderEntity orderInfo = deductPlan.getPlusOrderEntity();
        String plusOrderSn = orderInfo.getOrderSn();
        PlusOrderDeductResEntity resEntity = new PlusOrderDeductResEntity();
        try {
            PlusPayTypeEnum deductFlag = deductPlan.getDeductFlag();
            MemberPlusSwitchControlEntity switchControl = deductPlan.getSwitchControl();
            // 借款单放款成功延迟划扣
            if (deductFlag.equals(PlusPayTypeEnum.PAY_TYPE_1) && DeductTypeEnum.PAY_TYPE_2.getCode()
                    .equals(switchControl.getDeductType())) {
                String loanOrderSn = deductPlan.getOrderSn();
                commonHandler.saveDeductDelayPlan(orderInfo, switchControl, loanOrderSn,
                        "会员月卡-延迟划扣", "延迟划扣");
                resEntity.setRemark("走延迟划扣-保存逻辑");
                resEntity.setOptStatus(DeductResultStateEnum.NO.getCode());
                return resEntity;
            }
            // 获取默认卡划扣申请
            PlusOrderDeductResEntity getResEntity = getDeductApplyEventByDefaultCard(orderInfo,
                    deductPlan);
            if (!DeductResultStateEnum.SUCCESS.getCode().equals(getResEntity.getOptStatus())) {
                return getResEntity;
            }
            resEntity.setSeparateEntity(getResEntity.getSeparateEntity());
            // 组装划扣数据
            PlusPayDeductEvent deductEvent = converter.toPlusPayDeductEvent(
                    getResEntity.getSeparateEntity(), null);
            getResEntity.getSeparateEntity().getItems().forEach(item -> {
                // 只放清分主体
                if (item.getSupplierType().equals(SupplierTypeEnum.QF.getCode())) {
                    deductEvent.getSplitItems().add(converter.toPlusSplitItemEvent(item));
                }
            });
            PlusOrderPayDeductRespEvent respEvent = fmsRepository.deduct(deductEvent);
            resEntity.setDeductRespEvent(respEvent);
            // 成功
            if (!Objects.isNull(respEvent) && StringUtils.equals(PayStateCodeEnum.I.getCode(),
                    respEvent.getState())) {
                log.info("会员月卡划扣申请同步返回成功：{}", plusOrderSn);
                resEntity.setRemark("划扣申请成功");
                resEntity.setOptStatus(DeductResultStateEnum.SUCCESS.getCode());
            } else {
                // 失败
                log.info("会员月卡划扣申请同步返回失败：{}", plusOrderSn);
                resEntity.setRemark("划扣申请失败");
                resEntity.setOptStatus(DeductResultStateEnum.FAIL.getCode());
            }
        } catch (Exception e) {
            log.info("会员月卡后付款会员划扣申请异常：{}", plusOrderSn, e);
            resEntity.setRemark("划扣申请异常");
            resEntity.setOptStatus(DeductResultStateEnum.ERROR.getCode());
            LogUtil.printLog("会员月卡后付款会员划扣申请异常：", e);
        }
        log.info("会员月卡划扣申请完成：{}，{}，{}", userId, plusOrderSn, deductPlan.getOrderSn());
        return resEntity;
    }

    @Override
    @Deprecated
    public PlusOrderDeductResEntity deductOld(PlusDeductEvent deductPlan) {
        log.info("会员月卡划扣开始：{}", JSON.toJSONString(deductPlan));
        Integer userId = deductPlan.getUserId();
        PlusOrderEntity orderInfo = deductPlan.getPlusOrderEntity();
        String plusOrderSn = orderInfo.getOrderSn();
        PlusOrderDeductResEntity resEntity = new PlusOrderDeductResEntity();
        try {
            PlusPayTypeEnum deductFlag = deductPlan.getDeductFlag();
            MemberPlusSwitchControlEntity switchControl = deductPlan.getSwitchControl();
            // 借款单放款成功延迟划扣
            if (deductFlag.equals(PlusPayTypeEnum.PAY_TYPE_1) && DeductTypeEnum.PAY_TYPE_2.getCode()
                    .equals(switchControl.getDeductType())) {
                String loanOrderSn = deductPlan.getOrderSn();
                commonHandler.saveDeductDelayPlan(orderInfo, switchControl, loanOrderSn,
                        "会员月卡-延迟划扣", "延迟划扣");
                resEntity.setRemark("走延迟划扣-保存逻辑");
                resEntity.setOptStatus(DeductResultStateEnum.NO.getCode());
                return resEntity;
            }
            // 获取payChannel
            PlusOrderShuntPo orderShunt = getOrderShunt(plusOrderSn, deductPlan.getConfigId());
            String payChannel = getDeductPayChannel(orderShunt);
            String source = getDeductPaySource(orderShunt);
            // 组装划扣数据
            PlusRealDeductEvent realDeductEvent = converter.toPlusRealDeductEvent(deductPlan,
                    orderInfo.getOrderAmount(), payChannel, source);
            PlusOrderDeductCallBackEvent payOrderVo = payExternalRepository.deduct(realDeductEvent);
            resEntity.setCallBackEvent(payOrderVo);
            // 成功
            if (!Objects.isNull(payOrderVo) && StringUtils.equals(PayStateEnum.S.getCode(),
                    payOrderVo.getStatus())) {
                log.info("会员月卡划扣同步返回成功：{}", plusOrderSn);
                resEntity.setRemark("同步划扣成功");
                resEntity.setOptStatus(DeductResultStateEnum.SUCCESS.getCode());
            } else {
                // 失败
                log.info("会员月卡划扣同步返回失败：{}", plusOrderSn);
                resEntity.setRemark("同步划扣失败");
                resEntity.setOptStatus(DeductResultStateEnum.FAIL.getCode());
            }
        } catch (Exception e) {
            log.info("会员月卡后付款会员划扣异常：{}", plusOrderSn, e);
            resEntity.setRemark("同步划扣异常");
            resEntity.setOptStatus(DeductResultStateEnum.ERROR.getCode());
            LogUtil.printLog("会员月卡后付款会员划扣异常：", e);
        }
        log.info("会员月卡划扣完成：{}，{}，{}", userId, plusOrderSn, deductPlan.getOrderSn());
        return resEntity;
    }

} 
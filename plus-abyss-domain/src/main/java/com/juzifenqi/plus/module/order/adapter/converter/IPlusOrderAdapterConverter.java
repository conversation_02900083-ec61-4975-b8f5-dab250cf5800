package com.juzifenqi.plus.module.order.adapter.converter;

import com.juzifenqi.plus.module.asserts.model.event.PlusMemberCardOpenEvent;
import com.juzifenqi.plus.module.order.application.ao.PlusRenewalPlanAo;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusMonthMemberRenewalPlanEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.refund.PlusOrderRefundInfoEntity;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderCancelEvent;
import com.juzifenqi.plus.module.order.repository.po.MemberPlusRepeatOrderPo;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 订单适配转换器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/5 10:28
 */
@Mapper
public interface IPlusOrderAdapterConverter {

    IPlusOrderAdapterConverter instance = Mappers.getMapper(IPlusOrderAdapterConverter.class);

    PlusOrderCancelEvent toCancelEvent(MemberPlusRepeatOrderPo orderPo);

    PlusMemberCardOpenEvent toPlusOrderCreateEvent(Integer userId, String plusOrderSn,
            Integer channelId, PlusProgramEntity programEntity);

    @Mappings({@Mapping(target = "orderSn", source = "orderSn"),
            @Mapping(target = "batchCancel", source = "batchCancel"),
            @Mapping(target = "needNotice", source = "needNotice")})
    PlusOrderRefundInfoEntity toPlusOrderRefundInfoEntity(String orderSn, Boolean batchCancel,
            Boolean needNotice, String refundSerialNo, Integer refundState, Integer refundType,
            Integer cancelType, Integer optUserId, String optUserName, Integer cancelReason);

    List<PlusRenewalPlanAo> toRenewalPlanAoList(List<PlusMonthMemberRenewalPlanEntity> renewalPlanEntities);
}

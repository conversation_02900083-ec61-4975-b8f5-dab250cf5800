package com.juzifenqi.plus.module.order.repository.external.acl;

import com.groot.utils.core.date.LocalDateTimeUtils;
import com.groot.utils.exception.LogUtil;
import com.juzifenqi.plus.config.PlusMqConfig;
import com.juzifenqi.plus.enums.JuziPlusEnum;
import com.juzifenqi.plus.module.asserts.model.MemberPlusQueryModel;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusInfoDetailEntity;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderRepository;
import com.juzifenqi.plus.module.order.model.contract.entity.MemberMqEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.external.IMessageExternalRepository;
import com.juzifenqi.plus.module.order.model.event.order.OrderRefundSecondEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderDefrayResultMqEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderMessageMqOutEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderMessageOutEvent;
import com.juzifenqi.plus.mq.producer.PlusMqProducer;

import java.util.*;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MessageExternalRepositoryAcl implements IMessageExternalRepository {

    @Autowired
    private IPlusOrderRepository plusOrderRepository;
    @Autowired
    private MemberPlusQueryModel plusQueryModel;
    @Autowired
    private PlusMqConfig         mqConfig;
    @Autowired
    private PlusMqProducer       plusMqProducer;

    private final static List<Integer> PLUS_OPEN_NOTICE_TYPE = Arrays.asList(1, 4, 5, 15, 16);

    @Override
    public void sendPlusMq(PlusOrderMessageMqOutEvent orderMessageOutEvent) {
        String orderSn = orderMessageOutEvent.getOrderSn();
        MemberMqEntity memberMqDto = new MemberMqEntity();
        memberMqDto.setOrderSn(orderSn);
        memberMqDto.setUserId(orderMessageOutEvent.getUserId());
        memberMqDto.setProgramId(orderMessageOutEvent.getProgramId());
        memberMqDto.setMemberType(orderMessageOutEvent.getNotifyType());
        memberMqDto.setConfigId(orderMessageOutEvent.getConfigId());
        memberMqDto.setFromSource("proxy");
        MemberPlusInfoDetailEntity memberPlusInfoDetail = plusQueryModel.getMemberPlusInfoDetail(
                orderSn);
        // 20221214 zjf 对外输出增加字段
        handlerMq(orderMessageOutEvent.getNotifyType(), orderSn, memberMqDto, memberPlusInfoDetail);
        // 2022-02-11 会员支持多渠道 gs
        memberMqDto.setChannel(
                memberPlusInfoDetail == null || memberPlusInfoDetail.getChannelId() == null ? orderMessageOutEvent.getChannelId()
                        : memberPlusInfoDetail.getChannelId());
        String tag = mqConfig.getTagMemberType();
        if (memberPlusInfoDetail != null && (
                JuziPlusEnum.EXPEDITE_CARD.getCode() == memberPlusInfoDetail.getConfigId()
                        || JuziPlusEnum.DOWNRATE_CARD.getCode()
                        == memberPlusInfoDetail.getConfigId())) {
            memberMqDto.setJxStartTime(
                    LocalDateTimeUtils.formatDateToStr(memberPlusInfoDetail.getJxStartTime()));
            memberMqDto.setJxEndTime(
                    LocalDateTimeUtils.formatDateToStr(memberPlusInfoDetail.getJxEndTime()));
            tag = mqConfig.getTagMemberTypeSu();
        }
        plusMqProducer.sendMqNotice(memberMqDto, tag);

    }

    @Override
    public void sendMessageCenter(PlusOrderMessageOutEvent orderMessageOutEvent) {
        Map<String, Object> params = new HashMap<>(4);
        params.put("channel", orderMessageOutEvent.getChannelId());
        params.put("pushNoteKey", orderMessageOutEvent.getPushNoteKey());
        params.put("userId", orderMessageOutEvent.getUserId());
        if (orderMessageOutEvent.getParam() != null && orderMessageOutEvent.getParam().length > 0) {
            params.put("params", orderMessageOutEvent.getParam());
        }
        plusMqProducer.sendInfoToMsgCenter(params, orderMessageOutEvent.getUserId());
    }

    @Override
    public void sendDefrayResult(PlusOrderDefrayResultMqEvent event) {
        plusMqProducer.sendDefrayResult(event);
    }

    @Override
    public void sendPlusOpenMq(Integer userId, Integer configId) {
        if (PLUS_OPEN_NOTICE_TYPE.contains(configId)) {
            plusMqProducer.sendPlusOpenMQ(userId);
        }
    }

    @Override
    public void sendSecondRefundMq(OrderRefundSecondEvent event) {
        plusMqProducer.sendSecondRefundMq(event);
    }


    /**
     * 处理mq内容
     */
    private void handlerMq(Integer memberType, String orderSn, MemberMqEntity memberMqDto,
            MemberPlusInfoDetailEntity detail) {
        try {
            log.info("开始处理开通-取消mq内容：{}", orderSn);
            if (memberType != 1 && memberType != 2) {
                log.info("开始处理开通-取消mq内容，memberType不处理：{}，{}", orderSn, memberType);
                return;
            }
            PlusOrderEntity orderInfo = plusOrderRepository.getByPlusOrderSn(orderSn);
            // 取消会员
            if (memberType == 2) {
                log.info("开始处理取消mq内容，{}", orderSn);
                if (orderInfo != null) {
                    memberMqDto.setCancelReason(orderInfo.getCancelReason());
                    memberMqDto.setOrderAmount(orderInfo.getOrderAmount());
                    memberMqDto.setRefundAmount(orderInfo.getRefundAmount());
                    memberMqDto.setOpenTime(orderInfo.getCreateTime());
                    memberMqDto.setBizSource(orderInfo.getBizSource());
                    memberMqDto.setOutOrderSn(orderInfo.getOutOrderSn());
                }
                memberMqDto.setCancelTime(new Date());
            } else {
                log.info("开始处理开通mq内容，{}", orderSn);
                // 开通会员
                memberMqDto.setOpenTime(new Date());
                if (orderInfo != null) {
                    memberMqDto.setOrderAmount(orderInfo.getOrderAmount());
                    memberMqDto.setDiscountRate(orderInfo.getDiscountRate());
                    memberMqDto.setOpenMode(orderInfo.getPayType());
                    memberMqDto.setBizSource(orderInfo.getBizSource());
                    memberMqDto.setOutOrderSn(orderInfo.getOutOrderSn());
                }
                if (detail != null) {
                    memberMqDto.setOpenType(detail.getPlusType());
                }
            }
            if (detail != null) {
                log.info("开始处理方案名称mq内容：{}", orderSn);
                memberMqDto.setProgramName(detail.getConfigName());
            }
        } catch (Exception e) {
            LogUtil.printLog("处理开通/取消/过期mq内容异常", e);
        }
    }
}

package com.juzifenqi.plus.module.program.repository.entity;

import com.juzifenqi.plus.enums.PlusProgramSendTypeEnum;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class PlusProgramEntity {

    private int id;

    /**
     * 前台方案名称
     */
    private String name;

    /**
     * 生效时间
     */
    private Date effectiveTime;

    /**
     * 划线价
     */
    private BigDecimal memberPrice;

    /**
     * 方案单价
     */
    private BigDecimal mallMobilePrice;
    /**
     * 首单首月优惠：0-关闭；1-开启
     */
    private int firstOrderDiscount;

    /**
     * 规则说明图片
     */
    private String ruleDescImg;

    /**
     * 状态，1-上架；2-下架
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人ID
     */
    private int createUserId;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 编辑时间
     */
    private Date updateTime;

    /**
     * 编辑人ID
     */
    private int updateUserId;

    /**
     * 编辑人姓名
     */
    private String updateUserName;

    /**
     * 首单首月价钱
     */
    private BigDecimal firstOrderPrice;

    /**
     * 方案状态， 0 未生效 1 生效
     */
    private int programmeStatus;

    /**
     * 方案天数
     */
    private int programmeDays;

    /**
     * 推荐标记 0 否  1 是
     */
    private int recommendedMarkup;

    /**
     * 会员权益Id
     */
    private String modelId;

    /**
     * 提额等级-和风控一致
     */
    private String grade;

    /**
     * 最低提额金额
     */
    private BigDecimal minmunAmount;

    /**
     * 跳转URL
     */
    private String jumpUrl;

    /**
     * 后台方案名称
     */
    private String backstageName;

    /**
     * 预计可省金额
     */
    private BigDecimal estimateSaveAmount;

    /**
     * 是否设置了挽回弹窗，1-是，0-否
     */
    private Integer beSetRecovery;

    /**
     * 挽回弹窗图片
     */
    private String recoveryImg;

    /**
     * 是否展示到会员主页, 1-是，0-否
     */
    private Integer showHomePage;

    /**
     * 是否展示购买记录 0 否 1 是
     */
    private Integer showBuyRecord;

    /**
     * 会员等级 1-至尊卡；2-年卡；3-月卡；4-半年卡；5-季卡
     */
    private Integer memberGrade;

    /**
     * 会员主题 1-金色；2-紫色；3-铜色；4-蓝色；5-黑色
     */
    private Integer programColor;

    /**
     * 方案唯一标识
     */
    private String signProgram;

    /**
     * 会员类型id plus-config
     */
    private Integer configId;

    /**
     * 渠道
     */
    private Integer channel;

    /**
     * 营销时间：最大2880分钟（48小时），用户认证失败48小时内展示失败卡会员
     */
    private Integer showTime;

    /**
     * 营销文案
     */
    private String marketContent;

    /**
     * 省钱文案
     */
    private String amountContent;

    /**
     * 会员说明
     */
    private String userExplain;

    /**
     * 会员规则
     */
    private String  userRules;
    /**
     * 是否支持后付款 1支持 2不支持
     */
    private Integer afterPayState;
    /**
     * 合同标识
     */
    private String  contractId;
    /**
     * 方案id
     */
    private Integer programId;

    /**
     * 方案名称
     */
    private String programName;

    /**
     * 续费价格
     */
    private BigDecimal renewPrice;

    /**
     * 权益包信息
     */
    private List<PlusProgramProfitsPackageObject> profitsPackageList;
    /**
     * 发放节点 1_开卡即发 2_后付款支付成功发放
     * @see PlusProgramSendTypeEnum
     */
    private Integer sendNode;

    /**
     * 支付方式
     */
    private List<Integer> payTypes;

    /**
     * 首付金额（分期支付场景使用）
     */
    private BigDecimal firstPayAmount;

    private Boolean checkFlag;

    /**
     * 月卡总期数
     */
    private Integer monthPeriod;

    /**
     * 月卡扣费天数
     */
    private Integer monthDeductionDay;

}

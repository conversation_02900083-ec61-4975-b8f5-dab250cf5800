# 会员下单流程梳理

## 流程概述

会员下单流程是一个涉及多个数据表协同工作的复杂业务流程，主要包括订单创建、支付处理、权益发放等核心环节。

## 1. 下单前准备阶段

### 1.1 用户选择会员方案
- 用户在前端选择会员类型（`config_id`）
- 选择具体的会员方案（`program_id`）
- 系统展示方案详情：价格、权益、有效期等

### 1.2 系统预检查
- 验证用户资格
- 检查是否有未完成的订单
- 计算折扣和优惠（如有）
- 确定订单类型：1-开通，2-续费，3-升级，4-初始化

## 2. 订单创建阶段

### 2.1 创建主订单记录
**涉及表：`plus_order_info`**

```sql
-- 创建会员订单主记录
INSERT INTO plus_order_info (
    user_id, channel_id, order_sn, order_type, 
    config_id, program_id, program_name,
    order_state, order_amount, program_price, 
    discount_rate, pay_type, start_time, end_time, biz_source
) VALUES (
    ?, ?, ?, ?, 
    ?, ?, ?,
    1, ?, ?, 
    ?, ?, ?, ?, ?
);
```

**关键字段说明：**
- `order_sn`：全局唯一订单号，作为业务主键
- `order_state`：初始状态为1（待支付）
- `order_amount`：用户实际需要支付的金额
- `start_time/end_time`：会员生效的时间区间

### 2.2 创建扩展信息（如需要）
**涉及表：`plus_order_ext_info`**

适用于特殊业务场景，如联名卡订单：
- 记录虚拟权益价值
- 保存支付成功跳转URL
- 存储第三方系统相关信息

### 2.3 创建快照记录
**涉及表：`plus_order_program_snapshot`、`plus_order_model_snapshot`**

```sql
-- 保存方案配置快照
INSERT INTO plus_order_program_snapshot (
    order_sn, config_id, program_id, send_node
) VALUES (?, ?, ?, ?);

-- 保存权益配置快照
INSERT INTO plus_order_model_snapshot (
    order_sn, config_id, program_id, model_id, model_name, model_value
) VALUES (?, ?, ?, ?, ?, ?);
```

**快照作用：**
- 确保订单数据一致性
- 防止后续配置变更影响已下单订单
- 支持历史订单的准确查询和处理

## 3. 支付处理阶段

### 3.1 发起支付
- 根据 `pay_type` 确定支付方式：
  - 1-全款：立即支付全额
  - 2-划扣：从用户账户余额扣除
  - 3-后付款：先开通权益，后续支付

### 3.2 支付回调处理
**更新表：`plus_order_info`**

```sql
-- 支付成功后更新订单状态
UPDATE plus_order_info SET 
    order_state = 2,  -- 支付成功
    pay_time = NOW(),
    pay_amount = ?,
    serial_number = ?  -- 支付流水号
WHERE order_sn = ?;
```

### 3.3 处理支付失败
- 订单状态保持为1（待支付）
- 记录失败原因
- 支持重新支付

## 4. 权益发放阶段

### 4.1 权益发放时机判断
根据 `send_node` 字段：
- 1-开卡即发：订单创建后立即发放
- 2-后付款支付成功发放：支付成功后发放

### 4.2 更新会员状态
**涉及表：`member_plus_info`**

```sql
-- 更新用户会员状态
UPDATE member_plus_info SET 
    config_id = ?,
    start_time = ?,
    end_time = ?,
    status = 1  -- 激活状态
WHERE user_id = ?;
```

### 4.3 发放具体权益
- 根据快照表中的权益配置
- 逐项发放权益到用户账户
- 记录权益发放日志

## 5. 关联订单处理

### 5.1 建立订单关联
**涉及表：`plus_order_relation`**

适用于特殊业务场景，如小额月卡结清返现：

```sql
-- 建立会员订单与借款订单关联
INSERT INTO plus_order_relation (
    plus_order_sn, order_sn, config_id, business_type
) VALUES (?, ?, ?, 1);
```

### 5.2 跨订单业务处理
- 监听关联订单状态变化
- 执行相应的业务逻辑
- 更新会员订单状态（如需要）

## 6. 异常处理流程

### 6.1 订单取消
```sql
-- 取消订单
UPDATE plus_order_info SET 
    order_state = 3,  -- 取消状态
    cancel_type = ?   -- 取消类型
WHERE order_sn = ?;
```

### 6.2 退款处理
- 检查退款条件
- 执行退款操作
- 回收已发放权益
- 更新订单和会员状态

### 6.3 数据一致性保障
- 使用数据库事务确保操作原子性
- 快照机制保证历史数据准确性
- 状态机模式控制订单状态流转

## 7. 核心表关联关系图

```
plus_order_info (主订单表)
    ├── plus_order_ext_info (扩展信息)
    ├── plus_order_relation (关联关系)
    ├── plus_order_program_snapshot (方案快照)
    ├── plus_order_model_snapshot (权益快照)
    └── member_plus_info (会员状态)
```

## 8. 关键业务规则

### 8.1 订单状态流转
```
待支付(1) → 支付成功(2) → 权益发放
    ↓
  取消(3)
```

### 8.2 支付方式处理
- **全款支付**：标准支付流程
- **划扣支付**：从用户余额直接扣除
- **后付款**：先享受权益，后续支付

### 8.3 订单类型处理
- **开通**：新用户首次购买
- **续费**：现有会员延长有效期
- **升级**：从低级会员升级到高级会员
- **初始化**：系统初始化订单

## 9. 监控和日志

### 9.1 关键指标监控
- 订单创建成功率
- 支付成功率
- 权益发放成功率
- 订单处理时长

### 9.2 异常告警
- 支付失败超过阈值
- 权益发放失败
- 数据一致性异常
- 系统响应超时

## 10. 优化建议

### 10.1 性能优化
- 对高频查询字段建立索引
- 使用缓存减少数据库压力
- 异步处理非关键业务逻辑

### 10.2 数据治理
- 定期清理过期快照数据
- 建立数据归档机制
- 完善数据备份策略
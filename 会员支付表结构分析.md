# 会员支付表结构分析

## 概述

本文档详细梳理了会员下单业务流程中涉及的所有数据库表结构，按照业务流程顺序组织，便于理解整个会员下单的数据模型。

## 1. 核心订单表

### 1.1 plus_order_info（会员订单主表）

**表描述**：存储会员订单的核心信息，是整个会员订单系统的主表

| 字段名 | 类型 | 描述 | 备注 |
|--------|------|------|------|
| id | int | 主键ID | 自增主键 |
| user_id | int | 用户ID | 关联用户表 |
| channel_id | int | 渠道ID | 标识订单来源渠道 |
| order_sn | varchar | 订单号 | 业务主键，全局唯一 |
| order_type | int | 订单类型 | 1-开通，2-续费，3-升级，4-初始化 |
| config_id | int | 会员类型ID | 关联会员类型配置 |
| program_id | int | 方案ID | 关联具体的会员方案 |
| program_name | varchar | 方案名称 | 冗余字段，便于查询 |
| order_state | int | 订单状态 | 1-待支付，2-支付成功，3-取消 |
| cancel_type | int | 取消类型 | 订单取消时的分类 |
| order_amount | decimal | 订单金额 | 用户实际需要支付的金额 |
| program_price | decimal | 方案价格 | 方案原价 |
| discount_rate | decimal | 折扣率 | 享受的折扣比例 |
| pay_time | datetime | 支付时间 | 支付成功的时间 |
| pay_amount | decimal | 支付金额 | 实际支付金额 |
| pay_type | int | 支付方式 | 1-全款，2-划扣，3-后付款 |
| start_time | datetime | 订单周期开始时间 | 会员生效开始时间 |
| end_time | datetime | 订单周期结束时间 | 会员生效结束时间 |
| biz_source | int | 业务来源 | 标识业务来源系统 |

**关联关系**：
- 与 `plus_order_ext_info` 通过 `order_sn` 一对一关联
- 与 `plus_order_relation` 通过 `order_sn` 一对多关联
- 与 `member_plus_info` 通过 `user_id` 和 `config_id` 关联
- 与 `plus_program` 通过 `program_id` 关联

**在下单流程中的作用**：订单创建的核心表，记录订单的基本信息和状态流转

## 2. 扩展和关联表

### 2.1 plus_order_ext_info（会员订单扩展表）

**表描述**：存储订单的扩展信息，主要用于联名卡等特殊业务场景

| 字段名 | 类型 | 描述 | 备注 |
|--------|------|------|------|
| id | int | 主键ID | 自增主键 |
| user_id | int | 用户ID | 关联用户 |
| channel_id | int | 渠道ID | 订单渠道 |
| order_sn | varchar | 会员订单号 | 关联主订单表 |
| order_amount | decimal | 会员价 | 会员部分的价格 |
| virtual_amount | decimal | 联名卡虚拟权益价 | 虚拟权益价值 |
| business_type | int | 业务类型 | 1-联名卡订单 |
| order_flag | int | 订单标识 | 1-会员合并后的新订单 |
| pay_type | int | 支付方式 | 支付类型标识 |
| pay_success_return_url | varchar | 支付成功跳转URL | 支付完成后的跳转地址 |
| serial_number | varchar | 支付流水号 | 第三方支付流水 |
| out_order_sn | varchar | 三方订单号 | 外部系统订单号 |

**关联关系**：
- 与 `plus_order_info` 通过 `order_sn` 一对一关联

**在下单流程中的作用**：存储特殊业务场景的扩展信息，如联名卡业务

### 2.2 plus_order_relation（会员订单关系表）

**表描述**：建立会员订单与借款订单的关联关系

| 字段名 | 类型 | 描述 | 备注 |
|--------|------|------|------|
| id | int | 主键ID | 自增主键 |
| plus_order_sn | varchar | 会员单号 | 会员订单号 |
| order_sn | varchar | 借款单号 | 借款订单号 |
| config_id | int | 会员类型ID | 会员类型标识 |
| business_type | int | 业务类型ID | 1-小额月卡结清返现 |

**关联关系**：
- 与 `plus_order_info` 通过 `plus_order_sn` 关联
- 与借款订单系统通过 `order_sn` 关联

**在下单流程中的作用**：建立会员订单与借款订单的业务关联，支持跨订单的业务逻辑

### 2.3 快照表系列

#### 2.3.1 plus_order_program_snapshot（订单方案快照表）

**表描述**：保存下单时的方案配置快照，确保订单数据的一致性

| 字段名 | 类型 | 描述 | 备注 |
|--------|------|------|------|
| id | int | 主键 | 自增主键 |
| order_sn | varchar | 会员订单编号 | 关联订单 |
| config_id | int | 会员类型ID | 会员类型 |
| program_id | int | 方案ID | 方案标识 |
| send_node | int | 发放节点 | 1-开卡即发，2-后付款支付成功发放 |

**在下单流程中的作用**：保存下单时刻的方案配置，防止后续方案变更影响已下单的订单

#### 2.3.2 plus_order_model_snapshot（订单方案权益关系快照）

**表描述**：保存下单时的权益配置快照

| 字段名 | 类型 | 描述 | 备注 |
|--------|------|------|------|
| id | int | 主键 | 自增主键 |
| order_sn | varchar | 会员订单编号 | 关联订单 |
| program_id | int | 方案ID | 方案标识 |
| model_id | int | 会员权益ID | 权益标识 |
| short_name | varchar | 权益简称 | 权益名称 |
| guide_copy | varchar | 引导文案 | 用户引导文字 |
| saving_copy | varchar | 省钱文案 | 省钱提示文字 |
| sort | int | 排序序号 | 显示顺序 |
| rule_explain | varchar | 规则说明 | 权益使用规则 |
| alert_state | int | 权益弹窗展示状态 | 是否弹窗提示 |
| send_type | int | 发放方式 | 1-系统发放，2-人工领取 |

**在下单流程中的作用**：保存下单时刻的权益配置，确保用户享受的权益不受后续配置变更影响

### 2.4 分流和清分相关表

#### 2.4.1 plus_order_shunt（分流信息表）

**表描述**：记录订单的分流和入账信息，支持多主体经营

| 字段名 | 类型 | 描述 | 备注 |
|--------|------|------|------|
| id | int | 主键 | 自增主键 |
| user_id | int | 用户ID | 用户标识 |
| channel_id | int | 渠道ID | 渠道标识 |
| config_id | int | 会员类型ID | 会员类型 |
| order_sn | varchar | 会员订单号 | 订单标识 |
| contract_no | varchar | 合同编号 | 合同标识 |
| contract_no_back | varchar | 合同签章完成后已回调编号 | 合同回调标识 |
| plan_in_supplier | int | 计划入账方 | 0-桔子，3-橡树黑卡 |
| in_supplier | int | 实际入账方 | 实际入账的主体 |
| plan_in_business_scene | int | 计划入账业务场景 | 业务场景标识 |
| pay_type | int | 支付成功的支付类型 | 支付方式 |

**在下单流程中的作用**：支持多主体分流经营，记录订单的分流和入账信息

#### 2.4.2 plus_order_separate（分账记录表）

**表描述**：记录订单的分账信息

| 字段名 | 类型 | 描述 | 备注 |
|--------|------|------|------|
| user_id | int | 用户ID | 用户标识 |
| order_sn | varchar | 订单号 | 订单标识 |
| shunt_supplier_id | int | 分流供应商ID | 供应商标识 |
| business_scene | int | 业务场景 | 场景标识 |

**在下单流程中的作用**：记录具体的分账信息，支持财务清分

## 3. 会员信息表

### 3.1 member_plus_info（付费会员周期表）

**表描述**：记录用户的会员周期信息，是会员身份的核心表

| 字段名 | 类型 | 描述 | 备注 |
|--------|------|------|------|
| id | int | 主键 | 自增主键 |
| user_id | int | 用户ID | 用户标识 |
| channel_id | int | 渠道ID | 渠道标识 |
| config_id | int | 会员类型ID | 会员类型 |
| start_time | datetime | 会员开始时间 | 会员生效时间 |
| end_time | datetime | 会员结束时间 | 会员到期时间 |
| status | int | 会员状态 | 会员当前状态 |

**关联关系**：
- 与 `plus_order_info` 通过 `user_id` 和 `config_id` 关联
- 与 `member_plus_info_detail` 通过 `user_id` 和 `config_id` 关联

**在下单流程中的作用**：记录用户的会员身份和有效期，是会员权益判断的基础

### 3.2 member_plus_info_detail（会员业务订单详情表）

**表描述**：记录会员的详细业务信息

| 字段名 | 类型 | 描述 | 备注 |
|--------|------|------|------|
| id | int | 主键 | 自增主键 |
| user_id | int | 用户ID | 用户标识 |
| channel_id | int | 渠道ID | 渠道标识 |
| config_id | int | 会员类型ID | 会员类型 |
| jx_status | int | 桔享状态 | 桔享会员状态 |

**在下单流程中的作用**：记录会员的详细业务状态信息

### 3.3 plus_program（会员方案表）

**表描述**：配置会员方案信息，定义可购买的会员产品

| 字段名 | 类型 | 描述 | 备注 |
|--------|------|------|------|
| id | int | 方案ID | 主键，方案唯一标识 |
| config_id | int | 会员类型ID | 关联会员类型 |
| program_name | varchar | 方案名称 | 方案显示名称 |
| program_price | decimal | 方案价格 | 方案原价 |
| status | int | 方案状态 | 方案是否可用 |

**关联关系**：
- 与 `plus_order_info` 通过 `id` 关联到 `program_id`
- 与 `plus_model` 通过方案权益关系表关联

**在下单流程中的作用**：提供可选择的会员方案，是下单的基础配置

## 4. 业务支撑表

### 4.1 支付相关表

#### 4.1.1 plus_order_pay_detail（订单支付详情表）

**表描述**：记录订单的支付详细信息

| 字段名 | 类型 | 描述 | 备注 |
|--------|------|------|------|
| order_sn | varchar | 订单号 | 关联订单 |
| pay_amount | decimal | 支付金额 | 实际支付金额 |
| pay_time | datetime | 支付时间 | 支付完成时间 |
| pay_status | int | 支付状态 | 支付结果状态 |

**在下单流程中的作用**：记录支付的详细信息，支持支付状态跟踪

#### 4.1.2 member_plus_repeat_order（会员重复支付表）

**表描述**：记录会员重复支付的订单信息，用于异常处理

| 字段名 | 类型 | 描述 | 备注 |
|--------|------|------|------|
| id | int | 主键 | 自增主键 |
| user_id | int | 用户ID | 用户标识 |
| user_name | varchar | 用户姓名 | 用户姓名 |
| channel_id | int | 渠道 | 渠道标识 |
| order_sn | varchar | 订单号 | 订单标识 |
| order_amount | decimal | 订单金额 | 订单金额 |
| config_id | int | 会员类别ID | 会员类型 |
| program_id | int | 方案ID | 方案标识 |
| program_name | varchar | 会员卡名 | 会员卡名称 |
| opt_state | int | 状态 | 0-待处理，1-已处理 |
| pay_type | int | 支付方式 | 支付类型 |

**在下单流程中的作用**：处理重复支付异常情况，确保用户资金安全

### 4.2 退款相关表

#### 4.2.1 plus_refund_record（退款记录表）

**表描述**：记录会员订单的退款信息

**在下单流程中的作用**：支持订单退款处理，记录退款流程

#### 4.2.2 plus_order_refund_info（订单退款信息表）

**表描述**：记录订单退款的详细信息

**在下单流程中的作用**：详细记录退款的各项信息

#### 4.2.3 plus_order_refund_deduction（会员退款抵扣记录表）

**表描述**：记录退款时的抵扣信息

| 字段名 | 类型 | 描述 | 备注 |
|--------|------|------|------|
| id | int | 主键ID | 自增主键 |
| order_sn | varchar | 会员订单号 | 关联订单 |
| refund_info_id | int | 会员退款记录ID | 关联退款记录 |
| request_no | varchar | 请求流水号 | 请求唯一标识 |
| decuction_order_sn | varchar | 抵扣订单号 | 抵扣订单标识 |
| deduct_trade_no | varchar | 抵扣减免交易流水号 | 交易流水 |
| deduct_state | int | 抵扣状态 | 1-抵扣中，2-抵扣成功，3-抵扣失败 |
| decuction_param | varchar | 抵扣请求参数 | 抵扣参数信息 |

**在下单流程中的作用**：处理退款时的抵扣逻辑，确保退款金额准确

### 4.3 营销和折扣表

#### 4.3.1 member_plus_discount_record（会员折扣记录表）

**表描述**：记录用户享受的会员折扣信息

**在下单流程中的作用**：记录用户享受的折扣，支持折扣策略执行

#### 4.3.2 plus_discount_condition（折扣条件表）

**表描述**：配置折扣的触发条件

**在下单流程中的作用**：定义折扣的触发条件，支持动态折扣计算

#### 4.3.3 plus_discount_conf（折扣配置表）

**表描述**：配置折扣的具体规则

**在下单流程中的作用**：定义具体的折扣规则，支持折扣金额计算

### 4.4 权益和模型表

#### 4.4.1 plus_model（会员权益模型表）

**表描述**：定义会员可享受的各种权益

**在下单流程中的作用**：定义会员权益内容，是权益发放的基础配置

#### 4.4.2 plus_market_chain（营销流程表）

**表描述**：配置营销活动的流程链路

**在下单流程中的作用**：支持营销活动的流程化执行

## 5. 业务流程中的表使用顺序

### 5.1 下单阶段
```
1. plus_program - 查询方案信息和价格
2. plus_order_info - 创建订单主记录
3. plus_order_ext_info - 创建订单扩展信息（如有需要）
4. plus_order_program_snapshot - 保存方案配置快照
5. plus_order_model_snapshot - 保存权益配置快照
```

### 5.2 支付阶段
```
1. plus_order_pay_detail - 记录支付详情
2. plus_order_info - 更新订单状态和支付信息
3. member_plus_repeat_order - 处理重复支付（异常情况）
```

### 5.3 开卡阶段
```
1. member_plus_info - 创建或更新会员周期信息
2. member_plus_info_detail - 创建会员详情记录
3. plus_order_relation - 建立订单关联关系（如需要）
```

### 5.4 分流清分阶段
```
1. plus_order_shunt - 记录分流信息
2. plus_order_separate - 记录分账信息
```

### 5.5 异常处理阶段
```
1. member_plus_repeat_order - 重复支付处理
2. plus_refund_record - 退款记录
3. plus_order_refund_info - 退款详情
4. plus_order_refund_deduction - 退款抵扣处理
```

## 6. 表关系图

```
plus_program (方案配置)
    ↓ (program_id)
plus_order_info (订单主表) ←→ plus_order_ext_info (订单扩展)
    ↓ (order_sn)
plus_order_program_snapshot (方案快照)
plus_order_model_snapshot (权益快照)
    ↓ (支付成功后)
member_plus_info (会员周期) ←→ member_plus_info_detail (会员详情)
    ↓ (业务关联)
plus_order_relation (订单关系)
plus_order_shunt (分流信息)
plus_order_separate (分账记录)
```

## 7. 总结

会员下单业务的数据模型设计体现了以下特点：

1. **分层设计**：核心订单表、扩展表、快照表、业务支撑表分层清晰
2. **数据一致性**：通过快照表保证订单数据的一致性
3. **业务扩展性**：通过扩展表和关系表支持复杂业务场景
4. **异常处理**：专门的异常处理表确保业务健壮性
5. **多主体支持**：分流和分账表支持多主体经营模式

这个数据模型完整支撑了会员下单的全生命周期，从订单创建、支付处理、会员开通到后续的分账清分，每个环节都有对应的数据表支撑，确保了业务数据的完整性和可追溯性。
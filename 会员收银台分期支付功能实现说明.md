# 会员收银台分期支付功能实现说明

## 功能概述

在现有会员分期支付基础功能上，实现收银台分期支付功能，支持两期分期支付模式：
- **第一期支付**：使用 `firstPayAmount`（首付金额）进行支付和分账，并保存分流清分信息
- **第二期支付**：复用第一期的分流清分信息，使用剩余金额进行支付

## 技术实现详情

### 1. 枚举扩展

#### 1.1 OrderPayActionEnum 枚举扩展
**文件**：`OrderPayActionEnum.java`
```java
PAY_ACTION_1(1, "主动支付"), 
PAY_ACTION_2(2, "系统划扣"),
PAY_ACTION_3(3, "分期首付"),    // 新增
PAY_ACTION_4(4, "分期二付"),    // 新增
```

### 2. 接口扩展

#### 2.1 新增分期第二期支付接口
**文件**：`IPlusOrderApi.java`
```java
/**
 * 获取分期支付第二期支付信息
 * <p>用于分期支付的第二期支付，复用第一期的分流清分信息</p>
 */
PlusAbyssResult<PlusOrderPayInfoResp> getInstallmentSecondPayInfo(String orderSn);
```

### 3. 核心业务逻辑实现

#### 3.1 支付信息生成逻辑修改
**文件**：`PlusOrderApplicationImpl.java`

**分期支付判断逻辑**：
```java
// 判断是否为分期支付
boolean isInstallmentPayment = PlusOrderPayTypeEnum.PAY_FIRST_PERIOD.getValue()
        .equals(plusOrderEntity.getPayType());

OrderPayActionEnum payAction;
String tradeName;
if (isInstallmentPayment) {
    // 分期支付：判断是第一期还是第二期
    List<PlusOrderSeparateEntity> existingSeparates = separateModel.getPlusOrderSeparate(orderSn);
    boolean isFirstPeriod = existingSeparates.isEmpty() || 
        existingSeparates.stream().noneMatch(s -> 
            OrderPayActionEnum.PAY_ACTION_3.getCode().equals(s.getOrderPayAction()));
    
    if (isFirstPeriod) {
        payAction = OrderPayActionEnum.PAY_ACTION_3; // 分期首付
        tradeName = "会员分期首付";
    } else {
        payAction = OrderPayActionEnum.PAY_ACTION_4; // 分期二付
        tradeName = "会员分期二付";
    }
} else {
    payAction = OrderPayActionEnum.PAY_ACTION_1; // 主动支付
    tradeName = "会员主动支付";
}
```

#### 3.2 第一期支付分账信息生成
```java
/**
 * 获取第一期支付分账信息
 */
private PlusOrderDeductResEntity getFirstPeriodSeparateInfo(PlusOrderEntity plusOrderEntity, 
        PlusDeductEvent deductPlan) {
    // 使用 firstPayAmount 作为支付金额
    BigDecimal firstPayAmount = plusOrderEntity.getFirstPayAmount();
    if (firstPayAmount == null || firstPayAmount.compareTo(BigDecimal.ZERO) <= 0) {
        throw new PlusAbyssException("分期支付首付金额不能为空或小于等于0");
    }
    
    // 临时修改订单金额为首付金额，用于分账计算
    BigDecimal originalAmount = plusOrderEntity.getOrderAmount();
    plusOrderEntity.setOrderAmount(firstPayAmount);
    
    try {
        // 生成分账信息
        PlusOrderDeductResEntity result = separateModel.getPlusOrderSeparateInfo(plusOrderEntity, deductPlan);
        return result;
    } finally {
        // 恢复原始订单金额
        plusOrderEntity.setOrderAmount(originalAmount);
    }
}
```

#### 3.3 第二期支付分账信息生成
```java
/**
 * 获取第二期支付分账信息
 */
private PlusOrderDeductResEntity getSecondPeriodSeparateInfo(PlusOrderEntity plusOrderEntity, 
        PlusDeductEvent deductPlan) {
    String orderSn = plusOrderEntity.getOrderSn();
    
    // 查询第一期支付的分账记录
    List<PlusOrderSeparateEntity> firstPeriodSeparates = separateModel.getPlusOrderSeparate(orderSn)
            .stream()
            .filter(s -> OrderPayActionEnum.PAY_ACTION_3.getCode().equals(s.getOrderPayAction()))
            .collect(Collectors.toList());
    
    if (firstPeriodSeparates.isEmpty()) {
        throw new PlusAbyssException("未找到第一期支付的分账记录，无法进行第二期支付");
    }
    
    // 获取第一期的分账信息作为模板
    PlusOrderSeparateEntity firstPeriodSeparate = firstPeriodSeparates.get(0);
    
    // 计算第二期支付金额 = 订单总金额 - 首付金额
    BigDecimal totalAmount = plusOrderEntity.getOrderAmount();
    BigDecimal firstPayAmount = plusOrderEntity.getFirstPayAmount();
    BigDecimal secondPayAmount = totalAmount.subtract(firstPayAmount);
    
    if (secondPayAmount.compareTo(BigDecimal.ZERO) <= 0) {
        throw new PlusAbyssException("第二期支付金额计算错误，金额不能小于等于0");
    }
    
    // 复用第一期的分流清分信息，但使用第二期的支付金额
    return createSecondPeriodSeparateInfo(plusOrderEntity, deductPlan, firstPeriodSeparate, secondPayAmount);
}
```

#### 3.4 第二期支付专用接口实现
```java
@Override
public PlusOrderPayInfoAo getInstallmentSecondPayInfo(String orderSn) {
    log.info("开始获取分期支付第二期支付信息 orderSn {}", orderSn);
    
    // 参数校验
    if (StringUtils.isBlank(orderSn)) {
        throw new PlusAbyssException("会员订单号不能为空");
    }
    
    // 查询订单信息
    PlusOrderEntity plusOrderEntity = plusOrderQueryModel.getByOrderSn(orderSn);
    if (plusOrderEntity == null) {
        throw new PlusAbyssException("请求的订单不存在");
    }
    
    // 校验订单是否为分期支付类型
    if (!PlusOrderPayTypeEnum.PAY_FIRST_PERIOD.getValue().equals(plusOrderEntity.getPayType())) {
        throw new PlusAbyssException("该订单不是分期支付订单");
    }
    
    // 校验订单状态
    if (!Objects.equals(PlusOrderStateEnum.WAIT_PAY.getCode(), plusOrderEntity.getOrderState())) {
        throw new PlusAbyssException("请求的订单非待支付状态");
    }
    
    // 校验是否已完成第一期支付
    List<PlusOrderSeparateEntity> firstPeriodSeparates = separateModel.getPlusOrderSeparate(orderSn)
            .stream()
            .filter(s -> OrderPayActionEnum.PAY_ACTION_3.getCode().equals(s.getOrderPayAction()) 
                    && SeparateStateEnum.SUCCESS.getCode().equals(s.getSeparateState()))
            .collect(Collectors.toList());
    
    if (firstPeriodSeparates.isEmpty()) {
        throw new PlusAbyssException("第一期支付尚未完成，无法进行第二期支付");
    }
    
    // 生成第二期支付信息
    PlusOrderPayInfoAo plusOrderPayInfoAo = buildInstallmentSecondPayInfo(orderSn, plusOrderEntity);
    
    log.info("结束获取分期支付第二期支付信息 {}", JSONObject.toJSONString(plusOrderPayInfoAo));
    return plusOrderPayInfoAo;
}
```

### 4. 业务流程说明

#### 4.1 第一期支付流程
1. **支付信息生成**：
   - 检测到分期支付类型（`PAY_FIRST_PERIOD`）
   - 判断为第一期支付（无已完成的分账记录）
   - 使用 `firstPayAmount` 作为支付金额
   - 生成分账信息并保存到 `plus_order_separate` 和 `plus_order_separate_item` 表

2. **支付处理**：
   - 调用收银台进行首付金额支付
   - 支付成功后更新分账状态
   - 保留分流清分信息供第二期使用

#### 4.2 第二期支付流程
1. **支付信息生成**：
   - 调用 `getInstallmentSecondPayInfo` 接口
   - 校验第一期支付已完成
   - 查询第一期的分流清分信息
   - 计算第二期支付金额（总金额 - 首付金额）
   - 复用第一期的分流清分配置

2. **支付处理**：
   - 使用第二期支付金额调用收银台
   - 使用相同的分流主体和清分配置
   - 支付成功后完成整个分期支付流程

### 5. 关键技术点

#### 5.1 分流清分信息复用
- 第一期支付时保存分流主体信息（`shuntSupplierId`、`businessScene`）
- 第二期支付时查询并复用这些信息
- 确保两期支付使用相同的分流清分配置

#### 5.2 金额计算和校验
- 第一期：使用 `firstPayAmount`
- 第二期：使用 `orderAmount - firstPayAmount`
- 严格校验金额的合法性

#### 5.3 状态管理
- 通过 `orderPayAction` 区分第一期（3）和第二期（4）支付
- 通过 `separateState` 校验第一期支付是否成功完成

#### 5.4 缓存策略
- 第一期和第二期支付信息分别缓存
- 使用不同的缓存键避免冲突
- 缓存时间设置为5秒，平衡性能和数据一致性

### 6. 异常处理

#### 6.1 业务异常
- 订单不存在或状态异常
- 非分期支付订单
- 第一期支付未完成
- 金额计算错误

#### 6.2 技术异常
- 分布式锁获取失败
- 分账信息生成失败
- 缓存操作异常

### 7. 测试要点

#### 7.1 功能测试
- 第一期支付信息生成和支付流程
- 第二期支付信息生成和支付流程
- 分流清分信息的正确复用
- 金额计算的准确性

#### 7.2 异常测试
- 各种异常场景的处理
- 并发访问的安全性
- 缓存失效的处理

#### 7.3 集成测试
- 与收银台系统的集成
- 与分账系统的集成
- 支付回调的处理

### 8. 部署注意事项

#### 8.1 向后兼容
- 新功能不影响现有支付流程
- 现有订单不受影响

#### 8.2 监控告警
- 添加分期支付相关的监控指标
- 设置异常告警机制

#### 8.3 数据一致性
- 确保分流清分信息的一致性
- 监控分期支付的完整性

## 总结

本次实现完成了会员收银台分期支付功能，在现有分期支付基础功能上，增加了收银台支付的分期支持。通过合理的架构设计和完善的异常处理，确保了分期支付的可靠性和一致性。

**核心特点**：
1. **分流清分信息复用**：第二期支付复用第一期的配置，确保一致性
2. **金额精确计算**：严格的金额计算和校验机制
3. **状态精确管理**：通过不同的支付动作区分两期支付
4. **完善的异常处理**：覆盖各种异常场景
5. **向后兼容**：不影响现有支付流程

package com.juzifenqi.plus.enums;

import java.util.Arrays;
import java.util.List;
import lombok.Getter;

/**
 * 任务状态
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/27 11:28
 */
@Getter
public enum PlusSendPlanTaskStatusEnum {
    WAIT_GRANT(1, "待发放"),
    GRANTING(2, "发放中"),
    GRANT_SUCCESS(3, "发放成功"),
    GRANT_FAIL(4, "发放失败"),
    ORDER_CANCEL(5, "订单取消失效"),
    ;

    private Integer value;
    private String name;

    PlusSendPlanTaskStatusEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public static List<Integer> expireTaskStatus() {
        return Arrays.asList(GRANT_SUCCESS.getValue(), GRANT_FAIL.getValue(), ORDER_CANCEL.getValue());
    }
}

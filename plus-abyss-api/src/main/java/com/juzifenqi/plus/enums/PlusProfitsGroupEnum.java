package com.juzifenqi.plus.enums;

import java.util.Objects;

/**
 * <AUTHOR> 权益发放后的类型
 */
public enum PlusProfitsGroupEnum {

    PLUS_COUPON(1, "券"),

    PLUS_CASH(2, "现金"),

    PLUS_CREDIT(3, "额度"),

    PLUS_SPEED(4, "加速"),

    PLUS_PRODUCT(5, "会员商品"),

    PLUS_PRODUCT_VIRTUAL(6, "虚拟商品"),

    PLUS_OTHER(7, "其他"),

    /**
     * 例：桔豆
     */
    PLUS_VIRTUAL(8, "虚拟货币"),
    ;

    private Integer value;
    private String  name;

    PlusProfitsGroupEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public static PlusProfitsGroupEnum getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (PlusProfitsGroupEnum plusProfitsGroupEnum : PlusProfitsGroupEnum.values()) {
            if (Objects.equals(plusProfitsGroupEnum.getValue(), value)) {
                return plusProfitsGroupEnum;
            }
        }
        return null;
    }
}

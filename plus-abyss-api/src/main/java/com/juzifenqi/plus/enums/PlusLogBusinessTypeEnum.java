package com.juzifenqi.plus.enums;

/**
 * 操作日志--业务类型
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/5 11:38
 */
public enum PlusLogBusinessTypeEnum {

    /**
     * 业务类型，从20开始，20以下aplha在用
     */
    INCOME(20, "入账"), OUTCOME(21, "出账"), SIGN_CALLBACK(22, "签章回调"), SUBMIT_SIGN(22,
            "发起签章"), PAY_CALLBACK(23, "支付回调"), CONTRACT_UPLOAD(24,
            "合同上传"), SAVE_PRE_BILL(25, "保存预入账信息"), SETTLE_CASHBACK_ADMIN(26,
            "分账列表-后台操作结清返现"), RENEW_PLAN(27, "续费计划"), PLUS_PRODUCT_LYSP(28,
            "0元商品操作"), QJHK(29, "区间还款配置"), DISCOUNT(30, "会员折扣"), SHUNT(31,
            "分流"), CHANNEL_MANAGER(32, "渠道管理"), SMS_CONFIG(33, "短信配置"), PRICE(34,
            "定价配置"), PROGRAM(35, "方案管理"), REPAY_CASHBACK_ADMIN(36,
            "分账列表-后台操作还款返现"), SUPPLIER(37, "分流主体"), SHUNT_ROUTE(38,
            "分流路由"), SUPPLIER_PUBLIC(39, "分流公共配置"), SEPARATE_SUPPLIER(40, "清分主体"), SETTLE(41, "结算"),
    ;

    private Integer code;
    private String  name;

    PlusLogBusinessTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (PlusLogBusinessTypeEnum value : PlusLogBusinessTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getName();
            }
        }
        return null;
    }

}

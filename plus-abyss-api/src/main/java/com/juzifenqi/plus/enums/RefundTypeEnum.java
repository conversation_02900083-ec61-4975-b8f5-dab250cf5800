package com.juzifenqi.plus.enums;

import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单退款方式枚举
 *
 * <AUTHOR>
 * @date 2024/9/2 16:11
 */
@Getter
@AllArgsConstructor
public enum RefundTypeEnum {

    YLT(1, "原路退"), DFTK(2, "换卡代付"), CDF(3, "纯代付");

    private final Integer code;
    private final String  desc;

    public static RefundTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (RefundTypeEnum e : values()) {
            if (Objects.equals(e.getCode(), code)) {
                return e;
            }
        }
        return null;
    }

}

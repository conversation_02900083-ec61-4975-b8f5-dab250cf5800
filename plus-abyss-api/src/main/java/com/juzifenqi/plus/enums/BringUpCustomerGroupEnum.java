package com.juzifenqi.plus.enums;

import lombok.Getter;

/**
 * BringUpCustomerGroupEnum
 *
 * <AUTHOR>
 * @Description 用户重提客群失败枚举
 * @Date 2024/06/21 15:34
 **/
@Getter
public enum BringUpCustomerGroupEnum {

    SUCCESS(1, "正常结束"), ORDER_EXIST(2, "用户存在在途订单或调用异常"), QUOTA_ERROR(3, "调用额度信息失败"), QUOTA_NULL(4,
            "用户可借额度为空"), AMOUNT_SMALL(5, "订单金额小于或等于3000"), RATE_ERROR(6,
            "查询差异化利率失败"), CREATE_ORDER_ERROR(7, "创建订单失败"), INSUFFICIENT_AMOUNT(8,
            "剩余借款金额不充足"), REJECT_ORDER_SN_NULL(8, "被拒绝的订单号为空"), ORDER_BASE_NULL(9,
            "借款订单信息不存在"), ORDER_BASE_STAGING_MONEY_NULL(10, "借款订单借款金额不存在"), ZP_CLOSE_ERROR(11,
            "资匹闭单失败/异常"), IN_BLACK_LIST(12, "用户在会员黑名单列表"), IS_XEYK_VIP(13,
            "用户是有效小额月卡身份"), RETRY(99, "重试处理中");

    private Integer code;

    private String desc;


    BringUpCustomerGroupEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}

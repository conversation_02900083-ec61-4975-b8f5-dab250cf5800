package com.juzifenqi.plus.enums;

import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 开启权益发放类型枚举
 *
 * <AUTHOR>
 * @date 2024-06-13 09:55
 */
@Getter
@AllArgsConstructor
public enum RdSendTypeEnum {

    JSSH(1, "加速审核", null), QYLYFF(2, "权益0元发放", "lyffRdzxEquityHandler"), HKFX(3,
            "还款返现", "hkfxRdzxEquityHandler"), NONE(4, "无权益", null),
    ;
    private final Integer code;
    private final String  msg;
    private final String className;

    /**
     * 获取策略类名
     */
    public static String getClassName(Integer code) {
        if (code == null) {
            return null;
        }
        for (RdSendTypeEnum value : RdSendTypeEnum.values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value.getClassName();
            }
        }
        return null;
    }

}

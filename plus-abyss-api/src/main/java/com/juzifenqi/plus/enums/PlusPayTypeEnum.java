package com.juzifenqi.plus.enums;

/**
 * 划扣类型枚举
 */
public enum PlusPayTypeEnum {

    /**
     *
     */
    PAY_TYPE_1(1, "放款划扣支付"), PAY_TYPE_2(2, "立即支付划扣"), PAY_TYPE_3(3, "用户主动支付"), PAY_TYPE_4(4,
            "催收划扣支付"), PAY_TYPE_5(5, "支付日划扣"), PAY_TYPE_6(6, "逾期划扣"), PAY_TYPE_7(7, "延时划扣");

    private int code;

    private String name;


    PlusPayTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


}

package com.juzifenqi.plus.enums;

/**
 * 会员开关枚举
 */
public enum PlusSwitchEnum {

    /**
     *
     */
    JSTK_PlUS_TOTAL(0, "极速退卡总开关"), EXPEDITE_PLUS_SWITCH(5, "加速卡极速退款开关",
            JuziPlusEnum.EXPEDITE_CARD.getCode()), SUC_PlUS_REFUND(8, "新人卡极速退款开关",
            JuziPlusEnum.SUCCESS_CARD.getCode()), RATE_PlUS_REFUND(9, "降息卡极速退款开关",
            JuziPlusEnum.DOWNRATE_CARD.getCode()), PlUS_AUTOMATIC_DEDUCT(20,
            "降息卡自动划扣开关"), EXPEDITE_PlUS_DEDUCT(21,
            "加速卡自动划扣开关"), PlUS_DETAIN_FRAME(30, "挽留弹窗开关"), PlUS_RAISE_CREDIT_SWITCH(
            31, "提额页面开关"), LEAD_ZY_SWITCH(32, "是否引流中原"), RDZX_LOCATION(33,
            "融担咨询卡活动展示位置配置"), NEW_PERSON_CARD_DEDUCT(34,
            "新人卡后台划扣开关"), ENJOY_CARD_DEDUCT(35, "桔享卡后台划扣开关"), FIXED_QUOTA_DEDUCT(
            36, "固额卡后台划扣开关"), ENJOY_SPEED_REFUND(38, "桔享卡极速退款",
            JuziPlusEnum.NEW_JUXP_CARD.getCode()), FIXED_QUOTA_SPEED_REFUND(39, "固额卡极速退款",
            JuziPlusEnum.QUOTA_CARD.getCode()), XEYK_AUTOMATIC_DEDUCT(45,
            "小额月卡后台划扣开关"), XEYK_SPEED_REFUND(46, "小额月卡极速退款",
            JuziPlusEnum.XEYK_CARD.getCode()),
            YT_DEDUCT(51, "宜通卡后台划扣开关"),
            YT_REFUND(52, "宜通卡极速退款", JuziPlusEnum.YITONG_CARD.getCode()),
            YK_DEDUCT(53, "会员月卡后台划扣开关"),
            YK_REFUND(54, "会员月卡极速退款", JuziPlusEnum.HYYK_CARD.getCode());

    private final Integer code;

    private final String name;

    /**
     * 开关配置对应的会员类型id
     */
    private Integer switchConfigId;

    PlusSwitchEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    PlusSwitchEnum(Integer code, String name, Integer switchConfigId) {
        this.code = code;
        this.name = name;
        this.switchConfigId = switchConfigId;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public Integer getSwitchConfigId() {
        return switchConfigId;
    }

    /**
     * 根据会员类型id获取对应的急速退款开关配置code
     */
    public static Integer getRefundSwitchCode(Integer configId) {
        if (configId == null) {
            return null;
        }
        for (PlusSwitchEnum value : PlusSwitchEnum.values()) {
            if (value.getSwitchConfigId() == null) {
                continue;
            }
            if (value.getSwitchConfigId().equals(configId)) {
                return value.getCode();
            }
        }
        return null;
    }
}

package com.juzifenqi.plus.enums;

import lombok.Getter;

/**
 * 商品详情页购买按钮状态
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/4 17:19
 */
@Getter
public enum BuyButtonStateEnum {

    /**
     * BuyButtonStateEnum
     */
    CAN_BUY(1, "可购买/领取"), OPEN_CARD(2, "请先开通会员"), RECEIVED(3,
            "已购买/领取过,无法再次购买/领取");

    private final Integer code;

    private final String name;


    BuyButtonStateEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}

package com.juzifenqi.plus.enums;

import lombok.Getter;

/**
 * 创单标识
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/21 11:26
 */
@Getter
public enum CreateOrderSceneEnum {
    LOAN(1, "借款首页"), LOAN_CONFIRM(2, "确认借款页"), PAY_DONE(3, "信用支付完成页"), LD(4,
            "落地页");

    private Integer code;
    private String  name;

    CreateOrderSceneEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}

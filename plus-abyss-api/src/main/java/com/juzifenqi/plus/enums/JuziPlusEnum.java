package com.juzifenqi.plus.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/6/29 20:29
 **/
@Getter
public enum JuziPlusEnum {

    /**
     * <p>1.新桔享 2.认证 3.还款 4.固定提额  5.加速 6.重提，7认证失败卡，</p>
     * <p>8认证成功卡，9降息卡，10融担咨询卡，11 桔会卡 12 小额月卡</p>
     * <p>13 权益卡，14 桔省卡, 15 宜通卡, 16 会员月卡</p>
     */
    NEW_JUXP_CARD(1, "新桔享", "桔享卡", "juxPlusHandler", "jxPlusStrategy",
            "juxApplicationHandler", "juXPlusService"), AUTH_CARD(2, "认证", "认证",
            null), REPAY_CARD(3, "还款", "还款", "repayPlusHandler", "repayPlusStrategy",
            "repayApplicationHandler", "repayPlusService"), QUOTA_CARD(4, "固定提额", "桔享卡",
            "quotaPlusHandler", "quotaPlusStrategy", "quotaApplicationHandler",
            "quotaPlusService"), EXPEDITE_CARD(5, "加速", "桔享卡", "expeditePlusHandler",
            "expeditePlusStrategy", "expApplicationHandler", "expeditePlusService"), RESUBMIT_CARD(
            6, "重提", "重提卡", "resubmitPlusHandler"), AUTHFAIL_CARD(7, "认证失败卡", "认证卡",
            "authFailPlusHandler"), SUCCESS_CARD(8, "认证成功卡", "successPlusHandler",
            "successPlusHandler", "successPlusStrategy",
            "successApplicationHandler"), DOWNRATE_CARD(9, "降息卡", "降息卡",
            "downRatePlusHandler"), RDZX_CARD(10, "融担咨询卡", "融单卡", "rdzxPlusHandler",
            "rdzxPlusStrategy", "rdzxApplicationHandler"), JH_CARD(11, "桔会卡", null,
            "jhPlusHandler", "jhPlusStrategy", null, "jhPlusService"), XEYK_CARD(12, "小额月卡",
            "桔享卡", "xeykPlusHandler", "xeykPlusStrategy", "xeykApplicationHandler",
            "xeykPluService"), PROFIT_CARD(13, "权益卡", "权益卡", "profitPlusHandler",
            "profitPlusStrategy", null, "profitPluService"), JUS_CARD(14, "桔省卡", "桔省卡",
            "jsPlusHandler", "jsPlusStrategy", null, "jsPlusService"), YITONG_CARD(15, "宜通卡", "宜通卡",
            "ytPlusHandler", "ytPlusStrategy", "ytApplicationHandler", "ytPlusService"),HYYK_CARD(16, "会员月卡", "会员月卡",
            "ykPlusHandler", "ykPlusStrategy", "ykApplicationHandler", "ykPlusService"),
    ;

    private int    code;
    private String name;
    private String group;
    /**
     * 订单能力策略
     */
    private String orderClassName;

    /**
     * 资产策略类名
     */
    private String assertClassName;

    /**
     * 订单应用层策略
     */
    private String appClassName;

    /**
     * 落地页策略
     */
    private String plusClass;

    JuziPlusEnum(int code, String name, String group, String className) {
        this.code = code;
        this.name = name;
        this.group = group;
        this.orderClassName = className;
    }

    JuziPlusEnum(int code, String name, String group, String className, String assertClassName,
            String appClassName) {
        this.code = code;
        this.name = name;
        this.group = group;
        this.orderClassName = className;
        this.assertClassName = assertClassName;
        this.appClassName = appClassName;
    }

    JuziPlusEnum(int code, String name, String group, String className, String assertClassName,
            String appClassName, String plusClass) {
        this.code = code;
        this.name = name;
        this.group = group;
        this.orderClassName = className;
        this.assertClassName = assertClassName;
        this.appClassName = appClassName;
        this.plusClass = plusClass;
    }

    public static JuziPlusEnum getByCode(Integer code) {
        for (JuziPlusEnum plus : values()) {
            if (code.equals(plus.getCode())) {
                return plus;
            }
        }
        return NEW_JUXP_CARD;
    }

    public static String getClassName(Integer code) {
        for (JuziPlusEnum plus : JuziPlusEnum.values()) {
            if (code == plus.getCode()) {
                return plus.getOrderClassName();
            }
        }
        return null;
    }

    public static String getAppClassName(Integer code) {
        for (JuziPlusEnum plus : JuziPlusEnum.values()) {
            if (code == plus.getCode()) {
                return plus.getAppClassName();
            }
        }
        return null;
    }

    public static String getAssertClassName(Integer code) {
        for (JuziPlusEnum plus : JuziPlusEnum.values()) {
            if (code == plus.getCode()) {
                return plus.getAssertClassName();
            }
        }
        return null;
    }

    public static String getNameByCode(Integer code) {
        for (JuziPlusEnum plus : JuziPlusEnum.values()) {
            if (code == plus.getCode()) {
                return plus.getName();
            }
        }
        return "付费会员";
    }

    public static String getPlusClassByCode(Integer code) {
        for (JuziPlusEnum plus : JuziPlusEnum.values()) {
            if (code == plus.getCode()) {
                return plus.getPlusClass();
            }
        }
        return null;
    }
}

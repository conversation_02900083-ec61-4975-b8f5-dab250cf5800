package com.juzifenqi.plus.enums;

/**
 * <AUTHOR>
 */
public enum PlusOrderPayTypeEnum {
    PAY_ALL(1, "全款支付", Boolean.FALSE),
    PAY_DEDUCT(2, "划扣", Boolean.FALSE),
    PAY_AFTER(3, "后付款", Boolean.TRUE),
    PAY_DEDUCT_ALL(4, "全款划扣", Boolean.FALSE),
    PAY_FIRST_PERIOD(5, "首期支付", Boolean.FALSE),
    ;
    private Integer value;
    private String name;
    /**
     * 是否后付费（后付费是下单即开卡）
     */
    private Boolean isPayAfter;


    PlusOrderPayTypeEnum(Integer value, String name, Boolean isPayAfter) {
        this.value = value;
        this.name = name;
        this.isPayAfter = isPayAfter;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public Boolean getPayAfter() {
        return isPayAfter;
    }

    public static PlusOrderPayTypeEnum getByValue(Integer value) {
        for (PlusOrderPayTypeEnum e : values()) {
            if (e.getValue().equals(value)) {
                return e;
            }
        }
        return null;
    }
}

package com.juzifenqi.plus.enums;

import lombok.Getter;

/**
 * 权益系统日志类型
 *
 * <AUTHOR>
 * @date 2024/5/29 上午11:02
 */
@Getter
public enum ProfitSystemLogTypeEnum {

    LYFF(4, "权益0元发放"), HKFX(5, "还款返现"), QJHK(6, "区间还款"),
    KKL_DATA(101, "开卡礼权益数据"),
    YXHB_DATA(102, "月享红包权益数据"),
    DMDS_DATA(103, "多买多送权益数据"),
    JJP_DATA(104, "拒就赔权益数据"),
    ;

    private final Integer code;

    private final String name;

    ProfitSystemLogTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

}

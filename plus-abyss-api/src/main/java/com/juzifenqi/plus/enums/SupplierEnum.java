package com.juzifenqi.plus.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 分流合作方枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/14 10:51
 */
public enum SupplierEnum {

    /**
     * 枚举，与virtual_product_supplier保持一致，桔子默认0
     */
    JDD(0, "桔子"), ZX(1, "子轩"), XSHK(3, "橡树黑卡", "oakVipHandler", "blackCardPay"), NY(4,
            "诺壹", null, "tonglian_nuoyi", "MEMBER_PAY"), YX(5, "友信", null, "tonglian_youxin",
            "MEMBER_PAY");

    private final int code;

    private final String desc;

    private String className;

    /**
     * 支付通道，划扣需要
     */
    private String deductPayChannel;

    /**
     * 来源，划扣需要
     */
    private String deductSource;


    SupplierEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    SupplierEnum(int code, String desc, String className, String deductPayChannel) {
        this.code = code;
        this.desc = desc;
        this.className = className;
        this.deductPayChannel = deductPayChannel;
    }

    SupplierEnum(int code, String desc, String className, String deductPayChannel,
            String deductSource) {
        this.code = code;
        this.desc = desc;
        this.className = className;
        this.deductPayChannel = deductPayChannel;
        this.deductSource = deductSource;
    }


    public String getDeductPayChannel() {
        return deductPayChannel;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getClassName() {
        return className;
    }

    public String getDeductSource() {
        return deductSource;
    }

    /**
     * 获取策略类名
     */
    public static String getClassName(Integer supplierId) {
        if (supplierId == null) {
            return null;
        }
        for (SupplierEnum value : SupplierEnum.values()) {
            if (value.getCode() == supplierId) {
                return value.getClassName();
            }
        }
        return null;
    }

    /**
     * 获取名称
     */
    public static String getName(Integer supplierId) {
        if (supplierId == null) {
            return null;
        }
        for (SupplierEnum value : SupplierEnum.values()) {
            if (value.getCode() == supplierId) {
                return value.getDesc();
            }
        }
        return null;
    }

    /**
     * 获取划扣payChannel
     */
    public static String getDeductPayChannelCode(Integer supplierId) {
        if (supplierId == null) {
            return null;
        }
        for (SupplierEnum value : SupplierEnum.values()) {
            if (value.getCode() == supplierId) {
                return value.getDeductPayChannel();
            }
        }
        return null;
    }

    /**
     * 获取划扣source
     */
    public static String getDeductSourceCode(Integer supplierId) {
        if (supplierId == null) {
            return null;
        }
        for (SupplierEnum value : SupplierEnum.values()) {
            if (value.getCode() == supplierId) {
                return value.getDeductSource();
            }
        }
        return null;
    }

    /**
     * 获取需要出入账的分流id集合
     */
    public static List<SupplierEnum> getOutSupplierList() {
        return Arrays.stream(SupplierEnum.values()).filter(e -> e.getCode() != ZX.getCode())
                .collect(Collectors.toList());
    }
}

package com.juzifenqi.plus.enums;

/**
 * <AUTHOR> 权益发放周期类型
 */
public enum PlusMemberProfitsGivePeriodsTypeEnum {

    /**
     * (当前时间，+00）
     */
    ONCE_IMMEDIATELY(1, "买完即发"),

    /**
     * (startTime, endTime]
     */
    ONCE_PERIODS(2, "全周期一次"),

    /**
     * (startTime, startTime + 1 mon], (startTime + 1 mon, startTime + 12 mon],直至到会员截止日
     */
    ONCE_MONTH(3, "每月一次"),

    /**
     * startTime = now () endTime = 当前会员卡周期结束时间
     */
    ONCE_PERIODS_2(4, "买完即发,全周期一次"),

    ;

    private Integer value;
    private String  name;

    PlusMemberProfitsGivePeriodsTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public static PlusMemberProfitsGivePeriodsTypeEnum getByValue(Integer value) {
        for (PlusMemberProfitsGivePeriodsTypeEnum e : values()) {
            if (e.value.equals(value)) {
                return e;
            }
        }
        // 默认全周期一次
        return ONCE_PERIODS;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}

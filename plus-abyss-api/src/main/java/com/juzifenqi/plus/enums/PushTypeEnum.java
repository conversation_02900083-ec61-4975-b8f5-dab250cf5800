package com.juzifenqi.plus.enums;

import lombok.Getter;

/**
 * 推送类型枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/4 10:35
 */
@Getter
public enum PushTypeEnum {

    /**
     * 1.新桔享 2.认证 3.还款 4.固定提额  5.加速 6.重提 7.认证失败 8.认证成功 9.降息卡 10 融担咨询卡 11 桔会卡 12 小额月卡 13 权益卡 14 桔省卡
     */
    PLUS_JX(1, "J", "plusType"), PLUS_FIX(4, "F", "fixCardType"), PLUS_UP(5, "SU",
            "expediteCardType"), PLUS_RC(6, "RC", "reSubmmitCardType"), PLUS_AUTH(2, "GE",
            "authCardType"), PLUS_REPAY(3, "R", "repayCardType"), PLUS_AF(7, "AF",
            "authFailCardType"), Plus_SUCC(8, "SUC", "authSucCardType"), PlUS_RATE(9, "PR",
            "downRateCardType"), PlUS_RDZX(10, "RDZX", "rdzxCardType"), PlUS_JH(11, "JH",
            "jhCardType"), PlUS_XEYK(12, "XEYK", "xeykCardType"), PlUS_PROFIT(13, "PROFIT",
            "profitCardType"), PlUS_JS(14, "JS", "jsCardType"),
    ;

    private Integer code;

    private String plusType;

    private String name;

    PushTypeEnum(Integer code, String plusType, String name) {
        this.code = code;
        this.plusType = plusType;
        this.name = name;
    }

    public static String getNameById(Integer id) {
        for (PushTypeEnum plus : PushTypeEnum.values()) {
            if (id.equals(plus.getCode())) {
                return plus.getName();
            }
        }
        return null;
    }

    public static String getTypeById(Integer id) {
        for (PushTypeEnum plus : PushTypeEnum.values()) {
            if (id.equals(plus.getCode())) {
                return plus.getPlusType();
            }
        }
        return null;
    }

    public static PushTypeEnum getPlusById(Integer id) {
        for (PushTypeEnum plus : PushTypeEnum.values()) {
            if (id.equals(plus.getCode())) {
                return plus;
            }
        }
        return null;
    }
}

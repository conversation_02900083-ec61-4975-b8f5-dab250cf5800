package com.juzifenqi.plus.enums;

import lombok.Getter;

/**
 * 重提客群，用户确认结果
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/1 14:18
 */
@Getter
public enum UserConfirmResultEnum {

    FQJK(1, "放弃借款"), QRTZ(2, "确认调整"),
    ;

    private final int    code;
    private final String desc;

    UserConfirmResultEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static UserConfirmResultEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (UserConfirmResultEnum value : UserConfirmResultEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }
}

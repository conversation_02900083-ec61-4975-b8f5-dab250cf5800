package com.juzifenqi.plus.enums;

/**
 * 取消会员原因枚举值
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/20 16:22
 */
public enum CancelReasonEnum {

    /**
     * 取消会员原因枚举值
     */
    CANCEL_REASON_ONE(1, "借款或商品单审核被拒"), CANCEL_REASON_TWO(2, "由于自身原因不想用了"), CANCEL_REASON_FOUR(4,
            "可借款额度减少"), CANCEL_REASON_FIVE(5, "点错了，用户由于操作原因"), CANCEL_REASON_SIX(6,
            "用户感觉没有实际作用"), CANCEL_REASON_SEVEN(7, "优惠力度不够"), CANCEL_REASON_EIGHT(8,
            "其他"), CANCEL_REASON_REFUND(9, "加速卡极速退款"), CANCEL_REASON_NEW_REFUND(10,
            "极速退款取消"), CANCEL_REASON_11(11, "放款时间太长"), CANCEL_REASON_12(12,
            "提额额度低"), CANCEL_REASON_13(13, "未提额"), CANCEL_REASON_14(14, "额度冻结"), CANCEL_REASON_15(
            15, "价格高"), CANCEL_REASON_16(16, "权益没有吸引力"), CANCEL_REASON_17(17,
            "无法借款(超龄、账号限制)"), CANCEL_REASON_18(18, "重复支付订单取消并退款"), CANCEL_REASON_19(19,
            "取消续费会员"), CANCEL_REASON_20(20, "30分钟未支付系统取消"), CANCEL_REASON_21(21,
            "会员失效后付款未付费系统取消"), CANCEL_REASON_22(22, "批量取消"), CANCEL_REASON_23(23,
            "催收系统主动取消"), CANCEL_REASON_24(24, "恶意投诉退费"), CANCEL_REASON_25(25,
            "点错了，不想开通会员"), OFFLINE_REFUND(26, "财务已完成线下退款，变更为取消"), OFFLINE_PAY(27,
            "用户已完成线下支付，变更为支付成功"), CANCEL_REASON_28(28, "小额月卡首单划扣失败无条件取消"), CANCEL_REASON_29(29,
            "单会员过期任务-无条件取消"), CANCEL_REASON_30(30, "三方申请退卡"), CANCEL_REASON_31(31, "会员订单过期取消申请"), CANCEL_REASON_32(
            32, "会员订单未过期取消申请"), CANCEL_REASON_33(
            33, "全款划扣失败取消");

    private int    code;
    private String name;

    CancelReasonEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return "其他";
        }
        for (CancelReasonEnum plus : CancelReasonEnum.values()) {
            if (code == plus.getCode()) {
                return plus.getName();
            }
        }
        return "其他";
    }
}

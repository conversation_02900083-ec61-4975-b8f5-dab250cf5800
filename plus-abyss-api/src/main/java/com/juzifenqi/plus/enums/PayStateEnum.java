package com.juzifenqi.plus.enums;

/**
 * 支付系统的支付状态
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/5 14:47
 */
public enum PayStateEnum {


    /**
     * 支付状态
     */
    S("S", "支付成功"), F("F", "支付失败"), QS("QS", "支付成功&扣额度成功"), QF("QF",
            "支付成功&扣额度失败");

    private String code;
    private String name;

    PayStateEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}

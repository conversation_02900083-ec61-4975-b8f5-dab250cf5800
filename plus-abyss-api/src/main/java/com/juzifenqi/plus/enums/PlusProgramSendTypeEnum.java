package com.juzifenqi.plus.enums;

/**
 * 会员方案-发放节点
 */
public enum PlusProgramSendTypeEnum {
    NOW(1, "开卡即发"),
    AFTER_PAY(2, "后付款支付成功发放"),
    ;
    private Integer value;
    private String name;


    PlusProgramSendTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public static PlusProgramSendTypeEnum getByValue(Integer value) {
        for (PlusProgramSendTypeEnum e : values()) {
            if (e.getValue().equals(value)) {
                return e;
            }
        }
        return null;
    }
}

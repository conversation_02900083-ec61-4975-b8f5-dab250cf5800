package com.juzifenqi.plus.enums;

/**
 * <AUTHOR> 2021/11/15 5:57 下午
 */
public enum ReceiveDetailTypeEnum {

    /**
     * 拒就赔
     */
    拒就赔(1, "拒就赔", 6),
    /**
     * 多买多送
     */
    多买多送(2, "多买多送", 4),
    /**
     * 开卡礼
     */
    开卡礼(3, "开卡礼", 1),
    /**
     * 还款优惠
     */
    还款优惠(4, "还款优惠", 12),
    /**
     * 月享礼
     */
    月享礼(5, "月享礼", 2),
    /**
     * 品牌专区
     */
    品牌专区(6, "品牌专区", 11),
    /**
     * 生日关怀
     */
    生日关怀(7, "生日关怀", 13),
    /**
     * 息费折扣
     */
    息费折扣(8, "息费折扣", 10),
    /**
     * 专属好礼
     */
    专属好礼(9, "专属好礼", 21),
    /**
     * 区间还款券
     */
    区间还款券(10, "区间还款券", 23);

    private Integer type;

    private String  des;
    /**
     * 详见 PlusModelEnum
     */
    private Integer modelId;

    public Integer getType() {
        return type;
    }

    public String getDes() {
        return des;
    }

    public Integer getModelId() {
        return modelId;
    }

    ReceiveDetailTypeEnum(Integer type, String des, Integer modelId) {
        this.type = type;
        this.des = des;
        this.modelId = modelId;
    }

    public static String getNameByType(Integer type) {
        for (ReceiveDetailTypeEnum plus : ReceiveDetailTypeEnum.values()) {
            if (plus.getType().equals(type)) {
                return plus.getDes();
            }
        }
        return "发放优惠券";
    }

    /**
     * 根据modelId获取type
     */
    public static Integer getTypeByModelId(Integer modelId) {
        for (ReceiveDetailTypeEnum plus : ReceiveDetailTypeEnum.values()) {
            if (plus.getModelId().equals(modelId)) {
                return plus.getType();
            }
        }
        return null;
    }
}

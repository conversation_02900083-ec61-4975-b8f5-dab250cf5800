package com.juzifenqi.plus.enums;

import java.util.EnumSet;

/**
 * 券类型
 */
public enum PlusCouponTypeEnum {

    /**
     * 1.开卡礼
     */
    KKL(1, "开卡礼", 1),
    /**
     * 2.月享
     */
    YX(2, "月享", 2),
    /**
     * 3.品牌专区
     */
    PPZQ(3, "品牌专区", 11),
    /**
     * 4.还款优惠
     */
    HKYH(4, "还款优惠", 12),
    /**
     * 5.生日关怀
     */
    SRGH(5, "生日关怀", 13),
    /**
     * 6.专属好礼
     */
    ZSHL(6, "专属好礼", 21);

    PlusCouponTypeEnum(int couponType, String desc, int modelId) {
        this.couponType = couponType;
        this.desc = desc;
        this.modelId = modelId;
    }

    private int    couponType;
    private String desc;
    private int    modelId;

    public int getCouponType() {
        return couponType;
    }

    public String getDesc() {
        return desc;
    }

    public int getModelId() {
        return modelId;
    }

    public static Integer getCouponType(Integer modelId) {
        if (modelId == null) {
            return 0;
        }
        EnumSet<PlusCouponTypeEnum> enumSet = EnumSet.allOf(PlusCouponTypeEnum.class);
        for (PlusCouponTypeEnum model : enumSet) {
            if (modelId == model.getModelId()) {
                return model.getCouponType();
            }
        }
        return 0;
    }

    public static PlusModelEnum getPlusModelType(int couponType) {
        EnumSet<PlusCouponTypeEnum> enumSet = EnumSet.allOf(PlusCouponTypeEnum.class);
        for (PlusCouponTypeEnum model : enumSet) {
            if (couponType == model.getCouponType()) {
                return PlusModelEnum.getPlusModelEnum(model.getModelId());
            }
        }
        return null;
    }

}

package com.juzifenqi.plus.enums;

import java.math.BigDecimal;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum PlusReachConditionTypeEnum {

    /**
     * 1. 小额月卡同序列支付笔数
     */
    XEYK_PAY_COUNT("XEYK_PAY_COUNT", "小额月卡续费支付笔数", "plusOrderPayFinishEventStrategy") {
        @Override
        public Boolean isReached(String reachCondition, String reachValue) {
            return Integer.parseInt(reachValue) >= Integer.parseInt(reachCondition);
        }
    },
    /**
     * 2. 关联订单是否结清
     */
    REL_LOAN_FINISH("REL_LOAN_FINISH", "关联订单是否结清", "jqfxFinishEventStrategy") {
        @Override
        public Boolean isReached(String reachCondition, String reachValue) {
            return Integer.parseInt(reachValue) >= Integer.parseInt(reachCondition);
        }
    },

    /**
     * 3. 小额月卡订单取消笔数
     */
    XEYK_CANCEL_COUNT("XEYK_PAY_COUNT", "小额月卡取消笔数", "plusOrderCancelFinishEventStrategy") {
        @Override
        public Boolean isReached(String reachCondition, String reachValue) {
            return Integer.parseInt(reachValue) >= Integer.parseInt(reachCondition);
        }
    },

    /**
     * 4. 多买多送订单信用支付成功订单金额-单笔
     */
    DMDS_PRODUCT_ORDER_MONEY("DMDS_PRODUCT_ORDER_MONEY", "多买多送商品订单支付成功订单金额",
            "dmdsTaskEventStrategy") {
        @Override
        public Boolean isReached(String reachCondition, String reachValue) {
            return new BigDecimal(reachValue).compareTo(new BigDecimal(reachCondition)) >= 0;
        }
    },

    /**
     * 5. 拒就赔达成条件-订单被拒笔数
     */
    JJP_ORDER_FK_REJECT("JJP_ORDER_FK_REJECT", "拒就赔达成条件-订单被拒笔数",
            "jjpTaskEventStrategy") {
        @Override
        public Boolean isReached(String reachCondition, String reachValue) {
            return new BigDecimal(reachValue).compareTo(new BigDecimal(reachCondition)) >= 0;
        }
    },

    /**
     * 6. 购物返现达成金额条件
     */
    GWFX_ORDER_MONEY("GWFX_ORDER_MONEY", "购物返现达成金额", "gwfxFinishEventStrategy") {
        // 购物返现不需要判断下单金额，所以只要下单就返现。这里达成永远返回true就行
        @Override
        public Boolean isReached(String reachCondition, String reachValue) {
            return true;
        }
    },

    /**
     * 7. 购物返现回退
     */
    GWFX_CANCEL_ORDER_MONEY("GWFX_ORDER_MONEY", "购物返现达成金额", "gwfxCancelEventStrategy") {
        // 购物返现不需要判断下单金额，所以只要下单就返现。这里达成永远返回true就行
        @Override
        public Boolean isReached(String reachCondition, String reachValue) {
            return true;
        }
    },

    /**
     * 8. 还款返现达成条件
     */
    REPAY_PERIODS("REPAY_PERIODS", "还款返现达成期数", "hkfxFinishEventStrategy") {
        @Override
        public Boolean isReached(String reachCondition, String reachValue) {
            return Integer.parseInt(reachValue) == Integer.parseInt(reachCondition);
        }
    },

    ;
    private String value;
    private String name;
    private String strategyClassName;

    PlusReachConditionTypeEnum(String value, String name, String strategyClassName) {
        this.value = value;
        this.name = name;
        this.strategyClassName = strategyClassName;
    }

    public static PlusReachConditionTypeEnum getByValue(String value) {
        for (PlusReachConditionTypeEnum type : values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 指标是否达成
     */
    public abstract Boolean isReached(String reachCondition, String reachValue);
}

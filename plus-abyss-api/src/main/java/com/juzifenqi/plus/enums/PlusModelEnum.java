package com.juzifenqi.plus.enums;

import java.util.Arrays;
import java.util.Collections;
import java.util.EnumSet;
import java.util.List;

/**
 * 权益模块信息
 *
 * <AUTHOR>
 * @date 2020/12/14 21:17
 */
public enum PlusModelEnum {

    /**
     * 权益模块的ID、名称、拼音简称
     */
    /**
     * 开卡礼，全周期发一次，手工领取, 券
     */
    KKL(1, "开卡礼", "kkl", PlusMemberProfitsGivePeriodsTypeEnum.ONCE_PERIODS_2,
            PlusProfitsGiveTypeEnum.MANUAL, PlusProfitsGroupEnum.PLUS_COUPON, "kklHandler"),
    /**
     * 月享，按月发放，系统发放,券
     */
    YX(2, "月享红包", "yxhb", PlusMemberProfitsGivePeriodsTypeEnum.ONCE_MONTH,
            PlusProfitsGiveTypeEnum.SYSTEM, PlusProfitsGroupEnum.PLUS_COUPON, "yxhbHandler"),
    /**
     * 半价商品
     */
    BJSP(3, "半价商品", "bjsp", PlusMemberProfitsGivePeriodsTypeEnum.ONCE_PERIODS,
            PlusProfitsGiveTypeEnum.MANUAL, PlusProfitsGroupEnum.PLUS_PRODUCT,
            "plusProductHandler"),

    /**
     * 多买多送，按月发放，手工领取
     */
    DMDS(4, "多买多送", "dmds", PlusMemberProfitsGivePeriodsTypeEnum.ONCE_MONTH,
            PlusProfitsGiveTypeEnum.MANUAL, PlusProfitsGroupEnum.PLUS_COUPON, "dmdsHandler"),
    /**
     * 生活权益，周期跟配置走，手工领取, 虚拟权益
     */
    SHQY(5, "生活权益", "shqy", null, PlusProfitsGiveTypeEnum.MANUAL,
            PlusProfitsGroupEnum.PLUS_PRODUCT_VIRTUAL, "shqyHandler"),
    /**
     * 拒就赔，每月一次，手工领取，券
     */
    JJP(6, "拒就赔", "jjp", PlusMemberProfitsGivePeriodsTypeEnum.ONCE_MONTH,
            PlusProfitsGiveTypeEnum.MANUAL, PlusProfitsGroupEnum.PLUS_COUPON, "jjpHandler"),
    /**
     * 会员商品,全周期一次,
     */
    HYZXJ(7, "会员专享价", "hyzxj", PlusMemberProfitsGivePeriodsTypeEnum.ONCE_PERIODS,
            PlusProfitsGiveTypeEnum.MANUAL, PlusProfitsGroupEnum.PLUS_PRODUCT),
    /**
     * 额度, 买完即发，系统发放
     */
    HYTE(8, "会员提额", "hyte", PlusMemberProfitsGivePeriodsTypeEnum.ONCE_IMMEDIATELY,
            PlusProfitsGiveTypeEnum.SYSTEM, PlusProfitsGroupEnum.PLUS_CREDIT, "hyteHandler"),
    /**
     * 额度(已不用)
     */
    ZXTE(9, "专享提额", "zxte", PlusMemberProfitsGivePeriodsTypeEnum.ONCE_IMMEDIATELY,
            PlusProfitsGiveTypeEnum.SYSTEM, PlusProfitsGroupEnum.PLUS_CREDIT),
    /**
     * 息费折扣, 有效期一次，系统发放，券
     */
    XFZKQ(10, "息费折扣券", "xfzkq", PlusMemberProfitsGivePeriodsTypeEnum.ONCE_PERIODS,
            PlusProfitsGiveTypeEnum.SYSTEM, PlusProfitsGroupEnum.PLUS_COUPON, "xfzkHandler"),
    /**
     * 品牌专区, 每月一次，系统发放，券
     */
    PPZQ(11, "品牌专区", "ppzq", PlusMemberProfitsGivePeriodsTypeEnum.ONCE_MONTH,
            PlusProfitsGiveTypeEnum.SYSTEM, PlusProfitsGroupEnum.PLUS_COUPON, "ppzqHandler"),
    /**
     * 还款优惠, 会员有效期一次，手工领取，券
     */
    HKYH(12, "还款优惠", "hkyh", PlusMemberProfitsGivePeriodsTypeEnum.ONCE_PERIODS_2,
            PlusProfitsGiveTypeEnum.MANUAL, PlusProfitsGroupEnum.PLUS_COUPON, "hkyhHandler"),
    /**
     * 生日关怀, 会员有效期一次，系统发放，券 发放规则：
     * <p>1、生日当天以前购买会员并认证成功的用户可在生日当天获得此权益，有效期内仅在生日当天发放一次（以认证信息生日为准）
     * <p>2、系统获取当天生日人员并在上午10点自动发放权益，同时push与站内信一起触达用户，消息模版：MM02-生日关怀权益发放
     */
    SRGH(13, "生日关怀", "srgh", PlusMemberProfitsGivePeriodsTypeEnum.ONCE_PERIODS,
            PlusProfitsGiveTypeEnum.SYSTEM, PlusProfitsGroupEnum.PLUS_COUPON, "srghHandler"),
    /**
     * 音视频专区
     */
    YPSP(14, "视频音频专区", "ypsp", null, null, PlusProfitsGroupEnum.PLUS_PRODUCT_VIRTUAL),
    /**
     * 生活卡券
     */
    SHKQ(15, "生活卡券", "shkq", null, null, PlusProfitsGroupEnum.PLUS_PRODUCT_VIRTUAL),
    /**
     * 游戏点卡
     */
    YXKD(16, "游戏卡点", "yxkd", null, null, PlusProfitsGroupEnum.PLUS_PRODUCT_VIRTUAL),
    /**
     * 充值专区
     */
    CZZQ(17, "充值专区", "czzq", null, null, PlusProfitsGroupEnum.PLUS_PRODUCT_VIRTUAL),
    /**
     * 加速审核
     */
    GKSH(18, "更快审核", "gksh", null, null, PlusProfitsGroupEnum.PLUS_SPEED, "gkshHandler"),
    /**
     * 用来看的，没啥用
     */
    GGED(20, "更高额度", "gged", null, null, null, "ggedHandler"),
    /**
     * 加速放款
     */
    GKFK(19, "更快放款", "gkfk", null, null, PlusProfitsGroupEnum.PLUS_SPEED, "gkfkHandler"),
    /**
     * 专属好礼,每月一次，手工领取, 券
     */
    ZSHL(21, "专属好礼", "zshl", PlusMemberProfitsGivePeriodsTypeEnum.ONCE_MONTH,
            PlusProfitsGiveTypeEnum.MANUAL, PlusProfitsGroupEnum.PLUS_COUPON, "zshlHandler"),
    /**
     * 降息，用来看的
     */
    JX(22, "降息", "jx", null, null, null, "jxHandler"),
    /**
     * 区间还款，会员有效期一次，系统发放，券
     */
    QJHK(23, "区间还款", "qjhk", PlusMemberProfitsGivePeriodsTypeEnum.ONCE_PERIODS,
            PlusProfitsGiveTypeEnum.SYSTEM, PlusProfitsGroupEnum.PLUS_COUPON, "qjhkHandler"),
    /**
     * 拒就退，用来看的
     */
    JJTK(25, "拒就退款", "jjtk", null, null, null, "jjtkHandler"),

    /**
     * 一元购
     */
    YYG(26, "一元购", "yyg", PlusMemberProfitsGivePeriodsTypeEnum.ONCE_PERIODS,
            PlusProfitsGiveTypeEnum.MANUAL, PlusProfitsGroupEnum.PLUS_PRODUCT, "yygHandler"),
    /**
     * 购物返现
     */
    GWFX(27, "购物返现", "gwfx", PlusMemberProfitsGivePeriodsTypeEnum.ONCE_IMMEDIATELY,
            PlusProfitsGiveTypeEnum.SYSTEM, PlusProfitsGroupEnum.PLUS_CASH, "gwfxHandler"),
    /**
     * 专属客服，用来看的
     */
    ZSKF(28, "专属客服", "zskf", null, null, null, "zskfHandler"),
    /**
     * 联名权益，虚拟权益
     */
    LMQY(29, "联名权益", "lmqy", PlusMemberProfitsGivePeriodsTypeEnum.ONCE_IMMEDIATELY, PlusProfitsGiveTypeEnum.SYSTEM,
            PlusProfitsGroupEnum.PLUS_PRODUCT_VIRTUAL, "lmqyHandler"),
    /**
     * 通信联名卡
     */
    TXLMK(30, "通信联名卡", "txlmk", null, PlusProfitsGiveTypeEnum.MANUAL,
            PlusProfitsGroupEnum.PLUS_PRODUCT_VIRTUAL, "txlmkHandler"),
    /**
     * 生日礼
     */
    SRL(31, "生日礼", "srl", null, null, null, "srlHandler"),

    /**
     * 结清返现
     */
    JQFX(32, "结清返现", "jqfx", PlusMemberProfitsGivePeriodsTypeEnum.ONCE_IMMEDIATELY,
            PlusProfitsGiveTypeEnum.SYSTEM, PlusProfitsGroupEnum.PLUS_CASH, "jqfxHandler"),

    /**
     * 渠道生活权益
     */
    QDSHQY(33, "渠道生活权益", "qdshqy", null, PlusProfitsGiveTypeEnum.MANUAL,
            PlusProfitsGroupEnum.PLUS_PRODUCT_VIRTUAL, "qdshHandler"),

    LYSP(34, "0元商品", "qdshqy", PlusMemberProfitsGivePeriodsTypeEnum.ONCE_PERIODS,
            PlusProfitsGiveTypeEnum.MANUAL, PlusProfitsGroupEnum.PLUS_PRODUCT_VIRTUAL,
            "lyspHandler"),

    LYFF(35, "权益0元发放", "lyff", PlusMemberProfitsGivePeriodsTypeEnum.ONCE_IMMEDIATELY,
            PlusProfitsGiveTypeEnum.SYSTEM, PlusProfitsGroupEnum.PLUS_PRODUCT_VIRTUAL,
            "lyffHandler"),

    HKFX(36, "还款返现", "hkfx", PlusMemberProfitsGivePeriodsTypeEnum.ONCE_IMMEDIATELY,
            PlusProfitsGiveTypeEnum.SYSTEM, PlusProfitsGroupEnum.PLUS_CASH, "hkfxHandler");


    private       Integer                              modelId;
    private       String                               modelName;
    private       String                               shortPY;
    private final PlusMemberProfitsGivePeriodsTypeEnum givePeriodsTypeEnum;
    private final PlusProfitsGiveTypeEnum              giveTypeEnum;
    private final PlusProfitsGroupEnum                 group;
    private       String                               profitClassName;

    PlusModelEnum(int modelId, String modelName, String shortPY,
            PlusMemberProfitsGivePeriodsTypeEnum givePeriodsTypeEnum,
            PlusProfitsGiveTypeEnum plusProfitsGiveTypeEnum, PlusProfitsGroupEnum group) {
        this.modelId = modelId;
        this.modelName = modelName;
        this.shortPY = shortPY;
        this.givePeriodsTypeEnum = givePeriodsTypeEnum;
        this.giveTypeEnum = plusProfitsGiveTypeEnum;
        this.group = group;
    }

    PlusModelEnum(int modelId, String modelName, String shortPY,
            PlusMemberProfitsGivePeriodsTypeEnum givePeriodsTypeEnum,
            PlusProfitsGiveTypeEnum plusProfitsGiveTypeEnum, PlusProfitsGroupEnum group,
            String profitClassName) {
        this.modelId = modelId;
        this.modelName = modelName;
        this.shortPY = shortPY;
        this.givePeriodsTypeEnum = givePeriodsTypeEnum;
        this.giveTypeEnum = plusProfitsGiveTypeEnum;
        this.group = group;
        this.profitClassName = profitClassName;
    }

    public Integer getModelId() {
        return modelId;
    }


    public String getModelName() {
        return modelName;
    }


    public String getShortPY() {
        return shortPY;
    }


    public PlusMemberProfitsGivePeriodsTypeEnum getGivePeriodsTypeEnum() {
        return givePeriodsTypeEnum;
    }

    public PlusProfitsGiveTypeEnum getGiveTypeEnum() {
        return giveTypeEnum;
    }

    public PlusProfitsGroupEnum getGroup() {
        return group;
    }

    public String getProfitClassName() {
        return profitClassName;
    }

    public static String getNameById(int modelId) {
        EnumSet<PlusModelEnum> enumSet = EnumSet.allOf(PlusModelEnum.class);
        for (PlusModelEnum model : enumSet) {
            if (modelId == model.getModelId()) {
                return model.getModelName();
            }
        }
        return null;
    }

    public static String getShortPYById(int modelId) {
        EnumSet<PlusModelEnum> enumSet = EnumSet.allOf(PlusModelEnum.class);
        for (PlusModelEnum model : enumSet) {
            if (modelId == model.getModelId()) {
                return model.getShortPY();
            }
        }
        return null;
    }

    public static PlusMemberProfitsGivePeriodsTypeEnum getHandlerById(int modelId) {
        EnumSet<PlusModelEnum> enumSet = EnumSet.allOf(PlusModelEnum.class);
        for (PlusModelEnum model : enumSet) {
            if (modelId == model.getModelId()) {
                return model.givePeriodsTypeEnum;
            }
        }
        return null;
    }


    public static PlusModelEnum getPlusModelEnum(int modelId) {
        EnumSet<PlusModelEnum> enumSet = EnumSet.allOf(PlusModelEnum.class);
        for (PlusModelEnum model : enumSet) {
            if (modelId == model.getModelId()) {
                return model;
            }
        }
        return null;
    }

    public static String getProfitClassName(int modelId) {
        EnumSet<PlusModelEnum> enumSet = EnumSet.allOf(PlusModelEnum.class);
        for (PlusModelEnum model : enumSet) {
            if (modelId == model.getModelId()) {
                return model.getProfitClassName();
            }
        }
        return null;
    }

    /**
     * 会员合并取消会员涉及到要重新计算的权益id列表
     */
    public static final List<Integer> MERGE_MODE_IDS = Arrays.asList(PlusModelEnum.YX.getModelId(),
            PlusModelEnum.PPZQ.getModelId(), PlusModelEnum.JJP.getModelId(),
            PlusModelEnum.DMDS.getModelId(), PlusModelEnum.ZSHL.getModelId());

    /**
     * 商品单收货需要处理的权益id列表
     */
    public static final List<Integer> RECEIVE_MODEL_IDS = Collections.singletonList(
            PlusModelEnum.GWFX.getModelId());

    /**
     * 多级分类虚拟权益
     */
    public static Boolean isLevelVirtualProfitModel(int modelId) {
        return modelId == QDSHQY.getModelId();
    }

    /**
     * 需要刷新缓存 支持上架后修改数据类型 渠道生活权益
     */
    public static Boolean needRefreshCacheModel(int modelId) {
        return modelId == QDSHQY.getModelId();
    }
}

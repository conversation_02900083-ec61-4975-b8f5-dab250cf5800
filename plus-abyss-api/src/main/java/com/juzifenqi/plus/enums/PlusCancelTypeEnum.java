package com.juzifenqi.plus.enums;

/**
 * 会员订单取消类型
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/30 10:03
 */
public enum PlusCancelTypeEnum {

    CONDITION(1, "有条件取消", "conditionCancelHandler", 1),
    NO_CONDITION(2, "无条件取消", "unConditionCancelHandler", 2),
    JS(3, "极速退款", "jsCancelHandler", 3),
    DELAY(11, "延迟退款", "delayCancelHandler", 4),
    RATIO(6, "按比例取消", "rateCancelHandler", 5),
    EXPIRE_PAYMENT(7, "过期会员取消","expireCancelHandler",null),
    NO_EXPIRE_PAYMENT(8, "未过期会员取消", "noExpireCancelHandler", 2),
    CUSTOMER_CANCEL(9, "财务已完成线下退款、变更为已取消", "customerCancelHandler", null),
    RE_PAY_CANCEL(5, "重复支付取消", "repeatCancelHandler", null),
    DEDUCT_FAIL_CANCEL(10, "全款划扣失败取消", null, null),
    UN_PAY_CANCEL(4, "未支付超时取消", null, null);

    private final int     value;
    private final String  name;
    private final String  className;
    /**
     * 取消场景code，取值与plus-aplha：CancelSceneConstant
     */
    private final Integer cancelSense;


    PlusCancelTypeEnum(int value, String name, String className, Integer cancelSense) {
        this.value = value;
        this.name = name;
        this.className = className;
        this.cancelSense = cancelSense;
    }


    public String getClassName() {
        return className;
    }

    public int getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public Integer getCancelSense() {
        return cancelSense;
    }

    public static PlusCancelTypeEnum getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (PlusCancelTypeEnum e : values()) {
            if (e.getValue() == value) {
                return e;
            }
        }
        return null;
    }

    public static String getClassName(int value) {
        for (PlusCancelTypeEnum e : values()) {
            if (e.getValue() == value) {
                return e.getClassName();
            }
        }
        return null;
    }

    /**
     *  非代付取消
     *  除过期会员取消和未过期会员取消
     * <AUTHOR>
     * @version 1.0
     * @date 2024/1/9 14:48
     */
    public static Boolean isNotDefrayCancel(int value) {
        return (value != EXPIRE_PAYMENT.getValue() && value != NO_EXPIRE_PAYMENT.getValue());
    }
}

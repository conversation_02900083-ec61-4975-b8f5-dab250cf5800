package com.juzifenqi.plus.enums;

/**
 * 权益日志事件枚举
 */
public enum OptEventEnum {

    /**
     * 1.新增权益 2.编辑权益 3.删除权益
     */
    OPT_ADD(1, "新增权益"),

    OPT_MDF(2, "编辑权益"),

    OPT_DEL(3, "删除权益"),

    OPT_DOWN(4, "下架权益"),

    OPT_UP(5, "上架权益"),

    OPT_COUPON_ADD(6, "新增优惠券"),

    OPT_COUPON_MDF(7, "编辑优惠券"),

    OPT_COUPON_DEL(8, "删除优惠券");

    private int    code;
    private String name;

    OptEventEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        for (OptEventEnum plus : OptEventEnum.values()) {
            if (code == plus.getCode()) {
                return plus.getName();
            }
        }
        return "未知";
    }

}

package com.juzifenqi.plus.enums;

/**
 * 会员订单状态
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/8 18:09
 */
public enum PlusOrderStateEnum {

    /**
     * plus_order_info.order_state状态
     */
    WAIT_PAY(1, "待支付"), PAY_SUCCESS(2, "支付成功"), CANCELED(3, "已取消"),
    ;

    private final int code;

    private final String desc;

    PlusOrderStateEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}

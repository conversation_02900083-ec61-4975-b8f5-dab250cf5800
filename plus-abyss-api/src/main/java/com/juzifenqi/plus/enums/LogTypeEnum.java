package com.juzifenqi.plus.enums;

/**
 * 日志类型
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/11 15:53
 */
public enum LogTypeEnum {

    /**
     * 枚举
     */
    GWFX(1, "购物返现"), LYSP(2, "0元商品权益相关"), LYFF(3, "权益0元发放");


    private Integer code;

    private String plusType;


    LogTypeEnum(Integer code, String plusType) {
        this.code = code;
        this.plusType = plusType;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getPlusType() {
        return plusType;
    }

    public void setPlusType(String plusType) {
        this.plusType = plusType;
    }

}

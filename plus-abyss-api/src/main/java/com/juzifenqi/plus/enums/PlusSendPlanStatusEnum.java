package com.juzifenqi.plus.enums;

/**
 * <AUTHOR>
 */
public enum PlusSendPlanStatusEnum {
    NOT_EFFECTIVE(1, "未生效"),
    CONDITIONAL(2, "条件待达成"),
    READY(3, "已就绪"),
    DONE(4, "发放完成"),
    CANCEL(5, "已过期"),
    INVALID(6, "已失效");

    private Integer value;
    private String name;

    PlusSendPlanStatusEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public static PlusSendPlanStatusEnum getType(Integer value) {
        for (PlusSendPlanStatusEnum statusEnum : PlusSendPlanStatusEnum.values()) {
            if (statusEnum.getValue().equals(value)) {
                return statusEnum;
            }
        }
        return null;
    }
}

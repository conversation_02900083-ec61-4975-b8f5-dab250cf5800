package com.juzifenqi.plus.enums;

import java.util.ArrayList;
import java.util.List;

/**
 * 临额类型新老code对应枚举
 *
 * <AUTHOR>
 */
public enum NewGradeEnum {

    /**
     * 会员等级199-021
     */
    MEMBER_199_GRADE("021", "MEMBER_QUOTA199_TEMP", 1),
    /**
     * 会员等级399-022
     */
    MEMBER_399_GRADE("022", "MEMBER_QUOTA399_TEMP", 1),
    /**
     * 会员节相关，已不再使用
     */
    ACTIVITY_QUOTA_GRADE("023", "ACTIVITY_QUOTA_TEMP", 1),
    /**
     * 会员节相关，已不再使用
     */
    MEMBER_ACTIVITY_QUOTA_GRADE("024", "MEMBER_ACTIVITY_QUOTA_TEMP", 1),
    /**
     * 固定提额
     */
    MEMBER_RE_QUOTA_TEMP("025", "MEMBER_RE_QUOTA_TEMP", 4),
    /**
     * 认证成功卡提固额 (认证成功卡已经不再售卖，此类型不再使用)
     */
    COMMON_QUOTA_GRADE("026", "COMMON_QUOTA", 8),
    /**
     * 融单咨询卡固定提额 (从2023年起就没有相关数据)
     */
    MEMBER_RDZX_TEMP("MEMBER_RDZX_TEMP", "MEMBER_RDZX_TEMP", 10),
    /**
     * 加速卡提额
     */
    MEMBER_JSK_TEMP("MEMBER_JSK_TEMP", "MEMBER_JSK_TEMP", 5),
    /**
     * 小额月卡提额
     */
    MEMBER_FXK_TEMP("MEMBER_FXK_TEMP", "MEMBER_FXK_TEMP", 12),

    /**
     * 还款卡提额
     */
    MEMBER_HKK_TEMP("MEMBER_HKK_TEMP", "MEMBER_HKK_TEMP", 3),

    /**
     * 宜通提额
     */
    MEMBER_YT_TEMP("15", "YTK_QUOTA_TEMP", 15),

    /**
     * 会员月卡提额
     */
    MEMBER_YK_TEMP("16", "MONTHLY_QUOTA_TEMP", 16),
    ;

    private String  oldGrade;
    private String  newGrade;
    private Integer configId;

    public Integer getConfigId() {
        return configId;
    }

    NewGradeEnum(String oldGrade, String newGrade, Integer configId) {
        this.oldGrade = oldGrade;
        this.newGrade = newGrade;
        this.configId = configId;
    }

    public String getOldGrade() {
        return oldGrade;
    }


    public String getNewGrade() {
        return newGrade;
    }


    public static String getNewGrade(String oldGrade) {
        for (NewGradeEnum grade : values()) {
            if (oldGrade.equals(grade.getOldGrade())) {
                return grade.getNewGrade();
            }
        }
        return "MEMBER_QUOTA199_TEMP";
    }

    public static List<String> getNewGradeByConfig(Integer configId) {
        List<String> result = new ArrayList<>();
        for (NewGradeEnum grade : values()) {
            if (configId.equals(grade.getConfigId())) {
                result.add(grade.getNewGrade());
            }
        }
        return result;
    }
}

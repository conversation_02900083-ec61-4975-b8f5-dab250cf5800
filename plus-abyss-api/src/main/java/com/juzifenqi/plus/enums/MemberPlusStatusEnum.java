package com.juzifenqi.plus.enums;

/**
 * <AUTHOR>
 * @date 2019/01/24
 */
public enum MemberPlusStatusEnum {
    /**
     *
     */
    PAST(0, "过期付费会员"), NORMAL(1, "正常付费会员"), CANCEL(2, "取消订单");

    private Integer code;

    private String periodType;


    private MemberPlusStatusEnum(Integer code, String periodType) {
        this.code = code;
        this.periodType = periodType;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }


}

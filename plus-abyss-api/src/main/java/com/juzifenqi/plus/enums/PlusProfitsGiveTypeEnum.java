package com.juzifenqi.plus.enums;

/**
 * <AUTHOR>
 */
public enum PlusProfitsGiveTypeEnum {
    SYSTEM(1, "系统发放"),
    MANUAL(2, "人工领取"),
    ;
    private Integer value;
    private String name;


    PlusProfitsGiveTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public static PlusProfitsGiveTypeEnum getByValue(Integer value) {
        for (PlusProfitsGiveTypeEnum e : values()) {
            if (e.getValue().equals(value)) {
                return e;
            }
        }
        return null;
    }
}

package com.juzifenqi.plus.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 订单扣减差价科目枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/19 16:25
 */
public enum OrderDeductItemEnum {

    VIRTUAL("virtual", "虚拟权益", "virtualDeductHandler"), LYSP("lysp", "0元商品",
            "lyspDeductHandler"), GWFX("gwfx", "购物返现", "gwfxDeductHandler");


    private final String code;

    private final String desc;

    private final String className;

    OrderDeductItemEnum(String code, String desc, String className) {
        this.code = code;
        this.desc = desc;
        this.className = className;
    }

    public String getClassName() {
        return className;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getClassByCode(String code) {
        for (OrderDeductItemEnum value : OrderDeductItemEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getClassName();
            }
        }
        return null;
    }

    public static OrderDeductItemEnum getByCode(String code) {
        for (OrderDeductItemEnum value : OrderDeductItemEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static List<String> getCodes() {
        return Arrays.stream(OrderDeductItemEnum.values()).map(OrderDeductItemEnum::getCode)
                .collect(Collectors.toList());
    }
}

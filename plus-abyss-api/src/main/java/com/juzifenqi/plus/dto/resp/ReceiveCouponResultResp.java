package com.juzifenqi.plus.dto.resp;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 领取优惠券结果
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/20 17:30
 */
@Data
public class ReceiveCouponResultResp implements Serializable {

    private static final long serialVersionUID = -4323840872398614440L;

    /**
     * 用户优惠券信息
     */
    private CouponUser couponUser;

    /**
     * 领取的优惠券可使用状态 1=可使用 3=已过期,对客展示
     * <p>按原优惠券领取接口返回</p>
     */
    private Integer status;

    /**
     * 领取的优惠券可使用开始时间,对客展示
     * <p>按原优惠券领取接口返回</p>
     */
    private String startTime;

    /**
     * 领取的优惠券可使用结束时间,对客展示
     * <p>按原优惠券领取接口返回</p>
     */
    private String endTime;

    /**
     * 用户优惠券信息
     */
    @Data
    public static class CouponUser implements Serializable {

        private static final long serialVersionUID = -725614239623246676L;

        /**
         * id
         */
        private Integer id;

        /**
         * 优惠券ID
         */
        private Integer couponId;

        /**
         * 优惠券序列号
         */
        private String couponSn;

        /**
         * 可使用次数（默认都是1次，不支持多次使用）
         */
        private Integer canUse;

        /**
         * 领取时间
         */
        private Date receiveTime;

        /**
         * 订单id(无订单赋0)
         */
        private Integer orderId;

        /**
         * 优惠券使用时间
         */
        private Date useTime;

        /**
         * 开始使用时间
         */
        private Date useStartTime;
        /**
         * 结束使用时间
         */
        private Date useEndTime;

        /**
         * 优惠券名称
         */
        private String couponName;
    }
}

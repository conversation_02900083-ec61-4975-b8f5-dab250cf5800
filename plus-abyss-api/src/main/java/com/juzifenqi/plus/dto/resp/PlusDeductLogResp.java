package com.juzifenqi.plus.dto.resp;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 划扣日志
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/4 18:23
 */
@Data
public class PlusDeductLogResp implements Serializable {

    private static final long serialVersionUID = -6325905807864725158L;

    /**
     * 主键Id
     */
    private Integer id;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 会员单号
     */
    private String plusOrderSn;

    /**
     * 订单号
     */
    private String orderSn;


    /**
     * 划扣结果:0失败 1成功
     */
    private Integer deductStatus;

    /**
     * 划扣结果信息
     */
    private String deductMsg;

    /**
     * 划扣银行卡id
     */
    private Integer bankId;

    /**
     * 银行卡号
     */
    private String bankSn;

    /**
     * 创建时间
     */
    private Date createTime;
}

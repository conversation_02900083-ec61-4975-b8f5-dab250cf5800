package com.juzifenqi.plus.dto.resp;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 返现流水resp
 *
 * <AUTHOR>
 * @date 2023/09/19 10:54
 **/
@Data
public class CashbackRecordPageResp implements Serializable {

    private static final long serialVersionUID = 42L;

    private Integer recordId;

    /**
     * 借款订单号
     */
    private String orderSn;

    /**
     * 会员订单号
     */
    private String plusOrderSn;

    /**
     * 用户id
     */
    private Integer userId;

    private Integer channelId;

    /**
     * 返现金额
     */
    private BigDecimal cashbackAmount;

    /**
     * 打款状态：1_打款中 3_打款成功 4_打款失败
     */
    private Integer payStatus;

    /**
     * 流水号
     */
    private String transactionId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 打款失败原因
     */
    private String payFailMsg;

    /**
     * 期数
     */
    private Integer periods;

    /**
     * 会员类型id
     */
    private Integer configId;

    /**
     * 权益类型id
     */
    private Integer modelId;

}
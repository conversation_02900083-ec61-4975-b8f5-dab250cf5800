package com.juzifenqi.plus.dto.resp;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 订单差价计算结果
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/19 16:39
 */
@Data
public class OrderDeductResultResp implements Serializable {

    private static final long serialVersionUID = -3723031909805694012L;

    /**
     * 扣减差价权益数量
     */
    private int profitsNum;

    /**
     * 扣减差价金额
     */
    private BigDecimal deductPrice;

    /**
     * 扣除差价项目key
     *
     * @see com.juzifenqi.plus.enums.OrderDeductItemEnum
     */
    private String itemKey;

    /**
     * 项目说明
     */
    private String itemValue;
}

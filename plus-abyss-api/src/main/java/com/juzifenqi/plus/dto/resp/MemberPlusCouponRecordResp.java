package com.juzifenqi.plus.dto.resp;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> 会员券记录
 */
@Data
public class MemberPlusCouponRecordResp implements Serializable {

    private static final long serialVersionUID = 7354812512346819721L;

    /**
     * 用户ID
     */
    private Integer memberId;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 发放计划id
     */
    private Integer sendPlanId;

    /**
     * 每个优惠券表的主键id
     */
    private Integer memberCouponId;

    /**
     * 还款卡领取id
     */
    private String couponNos;

    /**
     * 优惠券领取id
     */
    private Integer couponUserId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}

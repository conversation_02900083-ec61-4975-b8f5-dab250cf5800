package com.juzifenqi.plus.dto.resp;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 返现流水resp
 *
 * <AUTHOR>
 * @date 2023/09/19 10:54
 **/

public class MemberPlusCashBackRecordResp implements Serializable {

    private static final long serialVersionUID = 42L;

    /**
     * 主键
     */
    private Integer recordId;

    /**
     * 借款订单号
     */
    private String orderSn;

    /**
     * 会员订单号
     */
    private String plusOrderSn;

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 会员类型id
     */
    private Integer modelId;

    /**
     *
     */
    private BigDecimal cashbackAmount;

    /**
     * 打款状态：1_打款中 2_打款成功 3_打款失败
     */
    private Integer payStatus;

    /**
     * 流水号
     */
    private String transactionId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 打款失败原因
     */
    private String payFailMsg;

    public String getPayFailMsg() {
        return payFailMsg;
    }

    public void setPayFailMsg(String payFailMsg) {
        this.payFailMsg = payFailMsg;
    }

    public Integer getRecordId() {
        return recordId;
    }

    public void setRecordId(Integer recordId) {
        this.recordId = recordId;
    }

    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn;
    }

    public String getPlusOrderSn() {
        return plusOrderSn;
    }

    public void setPlusOrderSn(String plusOrderSn) {
        this.plusOrderSn = plusOrderSn;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Integer getChannelId() {
        return channelId;
    }

    public void setChannelId(Integer channelId) {
        this.channelId = channelId;
    }

    public Integer getModelId() {
        return modelId;
    }

    public void setModelId(Integer modelId) {
        this.modelId = modelId;
    }

    public BigDecimal getCashbackAmount() {
        return cashbackAmount;
    }

    public void setCashbackAmount(BigDecimal cashbackAmount) {
        this.cashbackAmount = cashbackAmount;
    }

    public Integer getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(Integer payStatus) {
        this.payStatus = payStatus;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
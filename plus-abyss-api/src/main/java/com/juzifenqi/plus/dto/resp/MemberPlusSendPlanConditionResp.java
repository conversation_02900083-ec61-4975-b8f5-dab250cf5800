package com.juzifenqi.plus.dto.resp;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * @Version: 1.0
 * @Author:qixuezhi
 * @Date: 2023-09-18 17:07:22
 * @Description: 会员权益发放计划_达成条件表
 */
@Data
public class MemberPlusSendPlanConditionResp implements Serializable {

    private static final long serialVersionUID = -7892858507206080291L;

    /**
     * 发放计划id
     */
    private Integer memberPlusSendPlanId;

    /**
     * 达成条件_指标字段
     */
    private String conditionField;

    /**
     * 达成指标
     */
    private String reachCondition;

    /**
     * 达成实际值
     */
    private String ReachValue;

    /**
     * 是否达成
     */
    private Boolean isReached;

    /**
     * 达成是否触发发放任务
     */
    private Boolean isTaskTrigger;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
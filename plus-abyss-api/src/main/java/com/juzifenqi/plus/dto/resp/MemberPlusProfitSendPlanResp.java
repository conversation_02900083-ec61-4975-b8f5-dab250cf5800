package com.juzifenqi.plus.dto.resp;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 权益发放计划返回
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/18 16:53
 */
@Data
public class MemberPlusProfitSendPlanResp implements Serializable {

    private static final long serialVersionUID = -4314223265966102370L;

    /**
     * 主键ID
     */
    private Integer sendPlanId;

    /**
     * 用户ID
     */
    private Integer memberId;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 会员订单号
     */
    private String orderSn;

    /**
     * 方案id
     */
    private Integer programId;

    /**
     * 权益包id
     */
    private Integer packageId;

    /**
     * 会员类型id
     */
    private Integer configId;

    /**
     * 权益类型
     */
    private Integer modelId;

    /**
     * 会员身份id
     */
    private Integer memberPlusId;

    /**
     * 周期类型
     */
    private Integer periodsType;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 发放类型（1-系统发放 2-人工领取）
     */
    private Integer giveType;

    /**
     * 权益值类型（1-券 2-额度 3-现金 4-加速 5-商品 6-虚拟商品）
     */
    private Integer profitType;

    /**
     * 权益值（券id or 提额设置id or 加速类型 or 商品id or 虚拟商品id）
     */
    private String profitValue;

    /**
     * 状态（1-未生效 2-达成中 3-已就绪 4-已发放 5-已过期 6-已取消）
     */
    private Integer sendStatus;

    /**
     * 权益领取值,如：券类，领取值为couponUserId或couponNos
     */
    private String receiveValue;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 最大发放次数(预留)
     */
    private Integer maxSendTime = 1;

    /**
     * 已发放次数（预留）
     */
    private Integer sendTimes = 1;

    /**
     * 发放计划扩展信息
     */
    private MemberPlusSendPlanExtResp ext;

    /**
     * 发放计划券类权益领取信息
     */
    private MemberPlusCouponRecordResp couponRecord;

    /**
     * 达成条件
     */
    private List<MemberPlusSendPlanConditionResp> condition;
}

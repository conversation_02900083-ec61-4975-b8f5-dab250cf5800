package com.juzifenqi.plus.dto.resp;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 权益是否可买处理返回
 *
 * <AUTHOR>
 * @date 2023/05/09 11:52
 */
@Data
public class ProfitsCanBuyResp implements Serializable {

    private static final long serialVersionUID = 8986495053980097516L;


    /**
     * 是否可购买
     */
    private boolean isCanBuy;

    /**
     * 展示类型 0 不展示  1 权益已更新，可再次购买 2 权益已经用完 3 当前周期使用过，下个周期可使用
     */
    private int showType;

    /**
     * 权益使用日期
     */
    private Date useDate;

    /**
     * 下次可用日期
     */
    private Date nextCanUseDate;

    /**
     * 会员类型
     */
    private Integer configId;
}

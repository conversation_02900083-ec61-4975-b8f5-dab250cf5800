package com.juzifenqi.plus.dto.resp;

import java.io.Serializable;
import lombok.Data;

/**
 * 会员相关校验是否可买返回结构
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/19 11:52
 */
@Data
public class ProductCheckResultResp implements Serializable {

    private static final long serialVersionUID = -7805720663427338686L;

    /**
     * 是否可购买
     */
    private boolean canBuy;

    /**
     * 会员商品详情页购买按钮状态
     */
    private Integer buyButtonState;

    /**
     * 不能买的原因code
     */
    private Integer code;

    /**
     * 不能买的原因
     */
    private String reason;
}

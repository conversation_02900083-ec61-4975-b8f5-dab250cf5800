package com.juzifenqi.plus.dto.resp;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * 会员商品详情页
 * <p>目前是0元商品在用</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/4 17:14
 */
@Data
public class PlusProductDetailResp implements Serializable {

    private static final long serialVersionUID = 8648332202035065179L;

    /**
     * 商品id
     */
    private Integer productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品轮播图
     */
    private List<String> productImageList;

    /**
     * 划线价/市场价
     */
    private BigDecimal marketPrice;

    /**
     * 售价
     */
    private BigDecimal salePrice;

    /**
     * 商品主图
     */
    private String masterImg;

    /**
     * 商品详情页内容(富文本)
     */
    private String productDetail;

    /**
     * 购买按钮状态
     *
     * @see com.juzifenqi.plus.enums.BuyButtonStateEnum
     */
    private Integer buyButtonState;

    /**
     * sku列表
     */
    private List<PlusProductSkuResp> skuList;

    /**
     * sku信息
     */
    @Data
    public static class PlusProductSkuResp implements Serializable {

        private static final long serialVersionUID = -892022402557054452L;


        /**
         * 规格名称
         */
        private String specName;

        /**
         * 规格列表
         */
        private List<PlusProductSkuSpecResp> list;


        /**
         * 规格列表
         */
        @Data
        public static class PlusProductSkuSpecResp implements Serializable {

            private static final long serialVersionUID = 7662640463392463599L;

            /**
             * 是否已选择?
             */
            private Integer isSelected;

            /**
             * 规格id
             */
            private String specId;

            /**
             * 规格值
             */
            private String specValue;
        }
    }
}

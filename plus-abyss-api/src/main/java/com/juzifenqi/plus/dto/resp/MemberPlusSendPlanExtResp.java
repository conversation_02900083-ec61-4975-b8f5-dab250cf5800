package com.juzifenqi.plus.dto.resp;

import java.io.Serializable;
import lombok.Data;

/**
 * 发放计划扩展信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/26 16:00
 */
@Data
public class MemberPlusSendPlanExtResp implements Serializable {

    private static final long serialVersionUID = 5060933689233898389L;

    /**
     * 主键id
     */
    private Integer extId;

    /**
     * 发放计划表id
     */
    private Integer memberPlusSendPlanId;

    /**
     * 多买多送任务表id
     */
    private Integer dmdsTaskId;

    /**
     * 拒就赔任务表id
     */
    private Integer rejectTaskId;

    /**
     * 拒就赔类型 1-订单拒绝 2-认证拒绝
     */
    private Integer rejectType;

    /**
     * 下单笔数
     */
    private Integer orderNum;

    /**
     * 购物返现类型 1_现金 2_会员费
     */
    private Integer cashbackType;
}

package com.juzifenqi.plus.dto.resp;


import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * PlusVirtualProductResp
 *
 * <AUTHOR>
 * @Description
 * @Date 2024/06/19 10:09
 **/
@Data
public class PlusVirtualProductResp implements Serializable {

    private static final long serialVersionUID = 1836151970698588754L;

    /**
     * 方案ID
     */
    private Integer programId;

    /**
     * 商品id
     */
    private Integer productId;

    /**
     * 商品sku
     */
    private String productSku;

    /**
     * 商品主图
     */
    private String masterImg;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品介绍
     */
    private String productDetail;

    /**
     * 市场价（划线价）
     */
    private String marketPrice;

    /**
     * 售价
     */
    private BigDecimal discountPrice;

    /**
     * 充值code
     */
    private Integer rechargeCode;

    /**
     * 充值类型名称
     */
    private String rechargeName;

    /**
     * 充值方式 1_直充 2_卡密 3_酒店券直充
     */
    private Integer rechargeType;
}

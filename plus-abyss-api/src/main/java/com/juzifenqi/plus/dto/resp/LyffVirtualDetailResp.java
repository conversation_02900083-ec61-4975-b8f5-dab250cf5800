package com.juzifenqi.plus.dto.resp;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 查询权益0元发放虚拟商品详情返回
 *
 * <AUTHOR>
 * @date 2024/5/27 上午10:04
 */
@Data
public class LyffVirtualDetailResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 营销图片地址
     */
    private String imgUrl;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 会员价(售价 * 会员商品折扣)
     */
    private BigDecimal plusPrice;

    /**
     * 市场价
     */
    private BigDecimal plusMarketPrice;

    /**
     * 商品详情页内容(富文本)
     */
    private String productDetail;

    /**
     * 领取按钮状态
     *
     * @see com.juzifenqi.plus.enums.BuyButtonStateEnum
     */
    private Integer buyButtonState;

    /**
     * 充值code
     */
    private Integer rechargeCode;

    /**
     * 充值类型名称
     */
    private String rechargeName;

    /**
     * 充值方式 1_直充 2_卡密 3_酒店券直充
     */
    private Integer rechargeType;

}

package com.juzifenqi.plus.dto.resp;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 虚拟商品相关接口返回
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/19 11:52
 */
@Data
public class VirtualCheckResultResp implements Serializable {

    private static final long serialVersionUID = 1055046566846929767L;


    /**
     * 是否可购买 0 不可买  1 可买
     */
    private Integer canBuy = 0;

    /**
     * 提示类型
     * <p>-1、异常情况<p/>
     * <p>0、已领取该权益 （会员权益周期内已买过一次）<p/>
     * <p>1、下月再来 （当月已领取）<p/>
     * <p>2、有三方处理中订单 <p/>
     * <p>3、当月领取权益种类已达上限 <p/>
     * <p>4、有效期内领取权益种类已达上限 <p/>
     * <p>5、全部已领取 <p/>
     */
    private Integer tipType = 0;

    /**
     * 提示类型对应的提示语，目前是渠道生活权益使用到，商城的生活权益是前端根据tipType转换的
     */
    private String tigMsg;

    /**
     * 会员折扣
     */
    private BigDecimal discountRate;

    /**
     * 开通会员单号
     */
    private String plusOrderSn;

    /**
     * 方案名称
     */
    private String programName;

    /**
     * 权益类型id
     */
    private Integer profitTypeId;
}

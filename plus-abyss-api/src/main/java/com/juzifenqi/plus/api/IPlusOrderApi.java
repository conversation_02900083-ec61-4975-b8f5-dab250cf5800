package com.juzifenqi.plus.api;

import com.juzifenqi.plus.dto.req.CancelRenewPlanReq;
import com.juzifenqi.plus.dto.req.CancelRenewReq;
import com.juzifenqi.plus.dto.req.CreateOrderNoticeTaskReq;
import com.juzifenqi.plus.dto.req.PlusDeductForActiveReq;
import com.juzifenqi.plus.dto.req.PlusOrderCancelReq;
import com.juzifenqi.plus.dto.req.PlusOrderCreateReq;
import com.juzifenqi.plus.dto.req.PlusOrderRefundApplyReq;
import com.juzifenqi.plus.dto.req.PlusOrderRelationCreateReq;
import com.juzifenqi.plus.dto.req.PlusOrderRelationReq;
import com.juzifenqi.plus.dto.req.PlusPayCallbackReq;
import com.juzifenqi.plus.dto.req.PlusProductOrderCreateReq;
import com.juzifenqi.plus.dto.req.PlusRenewInfoReq;
import com.juzifenqi.plus.dto.req.PlusRenewalPlanInfoReq;
import com.juzifenqi.plus.dto.req.UpdOrderStateReq;
import com.juzifenqi.plus.dto.req.VirtualGoodsOrderCreateReq;
import com.juzifenqi.plus.dto.req.VirtualOrderCreateReq;
import com.juzifenqi.plus.dto.req.profits.ProductCheckReq;
import com.juzifenqi.plus.dto.resp.PlusAbyssResult;
import com.juzifenqi.plus.dto.resp.PlusOrderCancelResp;
import com.juzifenqi.plus.dto.resp.PlusOrderCreateResp;
import com.juzifenqi.plus.dto.resp.PlusOrderDeductResp;
import com.juzifenqi.plus.dto.resp.PlusOrderPayInfoResp;
import com.juzifenqi.plus.dto.resp.PlusOrderRefundInfoResp;
import com.juzifenqi.plus.dto.resp.PlusOrderRelationResp;
import com.juzifenqi.plus.dto.resp.PlusProductOrderCreateResp;
import com.juzifenqi.plus.dto.resp.PlusRenewInfoResp;
import com.juzifenqi.plus.dto.resp.PlusRenewalPlanInfoResp;
import com.juzifenqi.plus.dto.resp.ProductCheckResultResp;
import com.juzifenqi.plus.dto.resp.VirtualCheckResultResp;
import com.juzifenqi.plus.dto.resp.VirtualGoodsOrderCreateResp;
import com.juzifenqi.plus.dto.resp.VirtualOrderCreateResp;

import java.util.List;

/**
 * 会员订单api
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/30 11:23
 */
public interface IPlusOrderApi {

    /**
     * 创建会员订单
     */
    PlusAbyssResult<PlusOrderCreateResp> createOrder(PlusOrderCreateReq plusOrderCreateReq);

    /**
     * 创建虚拟权益订单
     */
    PlusAbyssResult<VirtualOrderCreateResp> createVirtualOrder(VirtualOrderCreateReq req);

    /**
     * 创建虚拟商品权益订单
     */
    PlusAbyssResult<VirtualGoodsOrderCreateResp> createVirtualGoodsOrder(
            VirtualGoodsOrderCreateReq req);

    /**
     * 创建0元商品订单
     */
    PlusAbyssResult<PlusProductOrderCreateResp> createPlusProductOrder(
            PlusProductOrderCreateReq req);

    /**
     * 检查虚拟权益是否能购买
     */
    PlusAbyssResult<VirtualCheckResultResp> canBuyVirtualProduct(VirtualOrderCreateReq req);

    /**
     * 能否购买会员商品校验
     * <p>目前是0元商品权益里的商品在用</p>
     */
    PlusAbyssResult<ProductCheckResultResp> canBuyProduct(ProductCheckReq req);

    /**
     * 能否购买会员商品
     * <p>目前是一元购和会员商品权益内的商品</p>
     */
    PlusAbyssResult<Boolean> canBuyPlusProduct(ProductCheckReq req);

    /**
     * 取消会员订单前预检查
     */
    PlusAbyssResult<PlusOrderCancelResp> checkPreCancel(PlusOrderCancelReq plusOrderCancelReq);

    /**
     * 取消会员订单
     * <p>有条件、无条件、按比例、急速、延迟、未过期、过期、批量取消</p>
     */
    PlusAbyssResult<PlusOrderCancelResp> cancelOrder(PlusOrderCancelReq plusOrderCancelReq);

    /**
     * 客服操作变更会员订单状态
     * <p>支付成功：后付款待支付订单由待支付变更为支付成功，用户线下转账给公司，可能是用户已注销桔多多app等原因</p>
     * <p>取消：由支付成功变更为取消，线下转账给用户，可能是用户的卡受限等原因</p>
     */
    PlusAbyssResult updOrderStateByCustomer(UpdOrderStateReq req);

    /**
     * 取消会员续费(小额月卡)
     */
    PlusAbyssResult cancelRenew(CancelRenewReq req);

    /**
     * 会员订单划扣
     */
    PlusAbyssResult<PlusOrderDeductResp> deduct(PlusDeductForActiveReq plusOrderDeductReq);

    /**
     * 会员订单支付回调
     * <p>订单域迁移完成后，删除此接口</p>
     */
    @Deprecated
    PlusAbyssResult payCallBack(PlusPayCallbackReq plusPayCallbackReq);

    /**
     * 会员订单未支付超时取消
     * <p>订单域迁移完成后，删除此接口</p>
     */
    @Deprecated
    PlusAbyssResult unPayCancelCallBack(String plusOrderSn);

    /**
     * 会员单与业务订单建立绑定关系
     */
    PlusAbyssResult buildOrderRelation(PlusOrderRelationCreateReq plusOrderRelationCreateReq);

    /**
     * 获取续费信息（非还款卡）
     */
    PlusAbyssResult<PlusRenewInfoResp> getRenewInfo(PlusRenewInfoReq plusRenewInfoReq);

    /**
     * 获取会员单关联信息
     */
    PlusAbyssResult<List<PlusOrderRelationResp>> getOrderRelation(PlusOrderRelationReq req);

    /**
     * 申请退款
     */
    PlusAbyssResult orderRefundApply(PlusOrderRefundApplyReq req);

    /**
     * 保存订单通知任务
     */
    PlusAbyssResult saveNoticeTask(CreateOrderNoticeTaskReq req);

    /**
     * 获取收银台支付请求信息
     * <p>订单中心调用</p>
     */
    PlusAbyssResult<PlusOrderPayInfoResp> getOrderPayInfo(String orderSn);

    /**
     * 获取会员订单退款信息
     */
    PlusAbyssResult<PlusOrderRefundInfoResp> getPlusOrderRefundApplyInfo(String orderSn);

    /**
     * 获取续费计划信息
     */
    PlusAbyssResult<PlusRenewalPlanInfoResp> getRenewalPlanInfo(PlusRenewalPlanInfoReq plusRenewPlanInfoReq);

    /**
     * 取消月卡会员续费计划
     */
    PlusAbyssResult cancelRenewalPlan(CancelRenewPlanReq cancelRenewPlanReq);

    /**
     * 会员月卡定时扣费任务
     */
    void monthCardDeductJob();
}

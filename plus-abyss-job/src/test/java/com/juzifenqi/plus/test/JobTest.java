package com.juzifenqi.plus.test;

import com.juzifenqi.plus.PlusAbyssJobApplication;
import com.juzifenqi.plus.api.IPlusOrderJobApi;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = PlusAbyssJobApplication.class)
public class JobTest {


    @Autowired
    private IPlusOrderJobApi jobApi;

    @Test
    public void tess() {
        jobApi.delayRefundExecute(10);
    }
}

package com.juzifenqi.plus.utils;

import ch.qos.logback.core.PropertyDefinerBase;
import com.groot.utils.exception.LogUtil;
import java.net.InetAddress;
import java.net.UnknownHostException;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2020/08/12
 */
@Slf4j
public class LogBackUtils extends PropertyDefinerBase {

    @Override
    public String getPropertyValue() {
        String ip = null;
        try {
            ip = InetAddress.getLocalHost().getHostName();
        } catch (UnknownHostException e) {
            LogUtil.printLog("获取ip异常", e);
        }
        return ip;
    }
}

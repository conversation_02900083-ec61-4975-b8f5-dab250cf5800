package com.juzifenqi.plus.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/9/14 10:17
 **/
@Component
public class DubboProperties {

    @Value("${dubbo.application.name}")
    public String applicationName;

    @Value("${dubbo.registry.address}")
    public String registryAddress;

    @Value("${dubbo.registry.protocol}")
    public String registryProtocol;

    @Value("${dubbo.reference.retries}")
    public Integer referenceRetries;

    @Value("${dubbo.reference.timeout}")
    public Integer referenceTimeout;

    /****************************************************************/

    @Value("${dubbo.abyss.group}")
    public String abyssGroup;
}

package com.juzifenqi.plus.config;

import com.alibaba.dubbo.config.ApplicationConfig;
import com.alibaba.dubbo.config.ProtocolConfig;
import com.alibaba.dubbo.config.RegistryConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2023/9/14 10:17
 **/
@Configuration
public class DubboBasicConfig {

    @Autowired
    private DubboProperties dubboProperties;

    /**
     * 应用配置
     */
    @Bean
    public ApplicationConfig getApplicationConfig() {
        ApplicationConfig application = new ApplicationConfig();
        application.setName(dubboProperties.applicationName);
        return application;
    }

    /**
     * 注册中心配置
     */
    @Bean
    public RegistryConfig getRegistryConfig() {
        RegistryConfig registry = new RegistryConfig();
        registry.setAddress(dubboProperties.registryAddress);
        registry.setCheck(false);
        registry.setProtocol(dubboProperties.registryProtocol);
        return registry;
    }
}

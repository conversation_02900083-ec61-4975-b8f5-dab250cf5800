package com.juzifenqi.plus.config;

import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2021/11/28
 */
@Configuration
@Slf4j
public class XxlJobConfig {

    @Value("${job.address}")
    public String jobAddress;
    @Value("${job.appname}")
    public String jobAppname;
    @Value("${job.port}")
    public int    jobPort;

    @Bean
    public XxlJobSpringExecutor xxlJobExecutor() {
        log.info(">>>>>>>>>>>>>>>>>>>>>>>xxljob-init-start>>>>>>>>>>>>>>>>>>>>>>>");
        XxlJobSpringExecutor xxlJobSpringExecutor = new XxlJobSpringExecutor();
        xxlJobSpringExecutor.setAdminAddresses(jobAddress);
        xxlJobSpringExecutor.setAppname(jobAppname);
        xxlJobSpringExecutor.setPort(jobPort);
        xxlJobSpringExecutor.setLogRetentionDays(7);
        log.info(">>>>>>>>>>>>>>>>>>>>>>>xxljob-init-end>>>>>>>>>>>>>>>>>>>>>>>");
        return xxlJobSpringExecutor;
    }
}

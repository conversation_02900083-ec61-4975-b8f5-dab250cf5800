package com.juzifenqi.plus.config;

import com.alibaba.dubbo.config.ApplicationConfig;
import com.alibaba.dubbo.config.ReferenceConfig;
import com.alibaba.dubbo.config.RegistryConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/9/14 10:17
 **/
@Component
public class DubboConfigBuilder {

    @Autowired
    private ApplicationConfig applicationConfig;
    @Autowired
    private RegistryConfig    registryConfig;
    @Autowired
    private DubboProperties   dubboProperties;

    public <T> ReferenceConfig<T> build(Class<T> cls, String group) {
        ReferenceConfig<T> reference = new ReferenceConfig<T>();
        reference.setApplication(applicationConfig);
        reference.setRegistry(registryConfig);
        reference.setInterface(cls);
        reference.setTimeout(dubboProperties.referenceTimeout);
        reference.setCheck(Boolean.FALSE);
        reference.setRetries(dubboProperties.referenceRetries);
        reference.setGroup(group);
        return reference;
    }

    public <T> ReferenceConfig<T> build(Class<T> cls, String group, String version) {
        ReferenceConfig<T> reference = new ReferenceConfig<T>();
        reference.setApplication(applicationConfig);
        reference.setRegistry(registryConfig);
        reference.setInterface(cls);
        reference.setTimeout(dubboProperties.referenceTimeout);
        reference.setCheck(Boolean.FALSE);
        reference.setRetries(dubboProperties.referenceRetries);
        reference.setGroup(group);
        if (!StringUtils.isEmpty(version)) {
            reference.setVersion(version);
        }
        return reference;
    }

    /**
     * 包含超时时间
     */
    public <T> ReferenceConfig<T> build(Class<T> cls, String group, Integer timeout) {
        ReferenceConfig<T> reference = new ReferenceConfig<T>();
        reference.setApplication(applicationConfig);
        reference.setRegistry(registryConfig);
        reference.setInterface(cls);
        reference.setTimeout(timeout);
        reference.setCheck(Boolean.FALSE);
        reference.setRetries(dubboProperties.referenceRetries);
        reference.setGroup(group);
        return reference;
    }

}

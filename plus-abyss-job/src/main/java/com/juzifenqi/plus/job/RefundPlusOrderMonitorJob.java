package com.juzifenqi.plus.job;


import com.alibaba.fastjson2.JSON;
import com.juzifenqi.plus.api.IPlusRefundRecordApi;
import com.juzifenqi.plus.api.IPlusSupplierJobApi;
import com.juzifenqi.plus.dto.resp.PlusAbyssResult;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Description: 会员订单退款监控Job
 * <AUTHOR>
 * @date created on 2025/08/02
 */
@Slf4j
@Component
public class RefundPlusOrderMonitorJob {



    @Resource
    private IPlusSupplierJobApi plusSupplierJobApi;

    /**
     * 会员订单退款监控
     */
    @XxlJob("refundPlusOrderMonitorJob")
    public ReturnT<String> refundPlusOrderMonitorJob(String param) {
        try{
            XxlJobLogger.log("会员订单退款监控开始 start");
            Integer lastId = StringUtils.isNotBlank(param) ? Integer.parseInt(param) : null;
            long t1 = System.currentTimeMillis();
            PlusAbyssResult<Boolean> result = plusSupplierJobApi.refundPlusOrderMonitor(lastId);
            long t2 = System.currentTimeMillis();
            XxlJobLogger.log("会员订单退款监控结束 end 耗时:{} ,结果:{}", t2 - t1, JSON.toJSONString(result));
        }catch (Exception e){
            XxlJobLogger.log("会员订单退款监控异常 end,异常:{}", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }


}

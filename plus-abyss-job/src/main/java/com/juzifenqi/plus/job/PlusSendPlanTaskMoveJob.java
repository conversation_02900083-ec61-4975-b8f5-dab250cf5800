package com.juzifenqi.plus.job;


import com.groot.utils.exception.LogUtil;
import com.juzifenqi.plus.api.IPlusOrderJobApi;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 权益发放计划任务job
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/27 14:13
 */
@Slf4j
@Component
public class PlusSendPlanTaskMoveJob {

    @Autowired
    private IPlusOrderJobApi jobApi;

    /**
     * 移动权益发放计划任务job
     */
    @XxlJob("plusSendPlanTaskMoveJob")
    public ReturnT<String> plusSendPlanTaskMoveJob(String s) {
        try {
            log.info("移动权益发放计划任务job处理开始");
            jobApi.movePlusSendPlanTasks();
            log.info("权移动益发放计划任务job处理结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("移动权益发放计划任务job处理异常 ", e);
        }
        return ReturnT.FAIL;
    }
}

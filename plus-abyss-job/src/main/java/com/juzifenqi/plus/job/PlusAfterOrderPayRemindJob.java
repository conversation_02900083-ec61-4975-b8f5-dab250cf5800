package com.juzifenqi.plus.job;


import com.groot.utils.exception.LogUtil;
import com.juzifenqi.plus.api.IPlusOrderJobApi;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * PlusAfterOrderPayRemindJob 
 * <AUTHOR> 
 * @Description 后付款订单支付提醒job
 * @Date 2024/06/24 14:28
**/
@Component
@Slf4j
public class PlusAfterOrderPayRemindJob {

    @Autowired
    private IPlusOrderJobApi orderJobApi;

    @XxlJob("plusAfterOrderPayHandler")
    public ReturnT<String> execute(String s) {
        try {
            log.info("----后付款订单支付提醒--job开始----");
            orderJobApi.waitPaySendSms();
            log.info("----后付款订单支付提醒--job结束----");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("后付款订单支付提醒 ", e);
        }
        return ReturnT.FAIL;
    }

    @XxlJob("xeykRenewRemindJob")
    public ReturnT<String> xeykRenewRemindJob(String s) {
        try {
            log.info("----小额月卡续费前5天发短信--job开始----");
            orderJobApi.xeykRenewRemind();
            log.info("----小额月卡续费前5天发短信--job结束----");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("小额月卡续费前5天发短信异常 ", e);
        }
        return ReturnT.FAIL;
    }
}

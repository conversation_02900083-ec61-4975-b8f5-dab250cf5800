package com.juzifenqi.plus.job;

import com.groot.utils.exception.LogUtil;
import com.juzifenqi.plus.api.IPlusSupplierJobApi;
import com.juzifenqi.plus.dto.resp.PlusAbyssResult;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * Description: 会员主体相关Job
 *
 * <AUTHOR>
 * @date created on 2025/6/19
 */
@Slf4j
@Component
public class PlusSupplierJob {

    @Resource
    private IPlusSupplierJobApi jobApi;

    // 最大重试次数
    private static final int MAX_RETRIES = 3;
    // 初始延迟 1s
    private static final long INITIAL_DELAY_SECOND = 60;
    // 退避因子（每次延迟时间倍增）
    private static final int BACKOFF_FACTOR = 2;

    /**
     * 会员主体退费账户余额不足报警Job
     * cron: 0 0 9,15,18 * * ?
     * 判断账户余额小于阈值进行飞书Robot报警
     * xxx-job重试不好使, 自写指数退避重试3次
     */
    @XxlJob("plusSupplierLimitAlertJob")
    public ReturnT<String> plusSupplierLimitAlertJob(String param) {
        boolean success = false;
        int attempt = 0;
        long delayMs = INITIAL_DELAY_SECOND;
        while (!success && attempt < MAX_RETRIES) {
            attempt++;
            try {
                log.info("尝试第 {} 次会员主体退费账户余额报警Job处理开始", attempt);
                PlusAbyssResult<Boolean> result = jobApi.plusSupplierLimitAlarmJob();
                if (result.getSuccess() && result.getResult()) {
                    log.info("会员主体退费账户余额报警Job处理成功");
                    return ReturnT.SUCCESS;
                }
                log.info("尝试第 {} 次会员主体退费账户余额报警Job处理失败", attempt);
                sleep(attempt, delayMs);
            } catch (Exception e) {
                LogUtil.printLog("尝试第 {} 次会员主体退费账户余额报警Job处理异常 ", attempt, e);
                sleep(attempt, delayMs);
            }
            // 延迟时间倍增
            delayMs *= BACKOFF_FACTOR;
        }
        return ReturnT.FAIL;
    }

    private void sleep(int attempt, long delayMs) {
        if (attempt >= MAX_RETRIES) {
            return;
        }
        try {
            TimeUnit.SECONDS.sleep(delayMs);
        } catch (InterruptedException ie) {
            Thread.currentThread().interrupt();
        }
    }
}
package com.juzifenqi.plus.job;


import com.groot.utils.exception.LogUtil;
import com.juzifenqi.plus.api.IPlusOrderJobApi;
import com.juzifenqi.plus.dto.req.RefundExecuteReq;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 急速/延迟退款job
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/28 18:06
 */
@Slf4j
@Component
public class PlusRefundRecordJob {

    @Autowired
    private IPlusOrderJobApi jobApi;

    /**
     * 急速退款job-正常任务
     */
    @XxlJob("plusRefundJob")
    public ReturnT<String> plusRefundJob(String s) {
        try {
            log.info("急速退款job处理开始");
            Integer size = StringUtils.isNotBlank(s) ? Integer.parseInt(s) : 30;
            RefundExecuteReq req = new RefundExecuteReq();
            req.setSize(size);
            req.setOptState(0);
            jobApi.refundExecute(req);
            log.info("急速退款job处理结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("急速退款job处理异常 ", e);
        }
        return ReturnT.FAIL;
    }

    /**
     * 急速退款job-异常任务重跑
     */
    @XxlJob("plusRefundRetryJob")
    public ReturnT<String> plusRefundRetryJob(String s) {
        try {
            log.info("急速退款异常job处理开始");
            Integer size = StringUtils.isNotBlank(s) ? Integer.parseInt(s) : 30;
            RefundExecuteReq req = new RefundExecuteReq();
            req.setSize(size);
            req.setOptState(2);
            req.setRetry(true);
            jobApi.refundExecute(req);
            log.info("急速退款异常job处理开始");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("急速退款异常job处理异常 ", e);
        }
        return ReturnT.FAIL;
    }

    /**
     * 急速退款job-支付宝退款延迟处理
     */
    @XxlJob("plusZFBRefundJob")
    public ReturnT<String> plusZFBRefundJob(String s) {
        try {
            log.info("急速退款-支付宝延迟处理job处理开始");
            Integer size = StringUtils.isNotBlank(s) ? Integer.parseInt(s) : 30;
            RefundExecuteReq req = new RefundExecuteReq();
            req.setSize(size);
            req.setOptState(5);
            jobApi.refundExecute(req);
            log.info("急速退款-支付宝延迟处理job处理结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("急速退款异常job处理异常 ", e);
        }
        return ReturnT.FAIL;
    }

    /**
     * 延迟退款job
     */
    @XxlJob("plusDelayRefundJob")
    public ReturnT<String> plusDelayRefundJob(String s) {
        try {
            log.info("延迟退款job处理开始");
            // 默认40个
            Integer size = StringUtils.isNotBlank(s) ? Integer.parseInt(s) : 40;
            jobApi.delayRefundExecute(size);
            log.info("延迟退款job处理开始");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("延迟退款job处理异常 ", e);
        }
        return ReturnT.FAIL;
    }

    /**
     * 订单中心状态变更mq任务处理
     */
    @XxlJob("orderMqSateJob")
    public ReturnT<String> orderMqSateJob(String s) {
        try {
            log.info("订单中心状态变更mq任务处理ob处理开始");
            jobApi.mqRecordExecute();
            log.info("订单中心状态变更mq任务处理job处理开始");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("订单中心状态变更mq任务处理job处理异常 ", e);
        }
        return ReturnT.FAIL;
    }
}

package com.juzifenqi.plus.job;

import com.juzifenqi.plus.api.IPlusOrderJobApi;
import com.juzifenqi.plus.dto.req.OrderNoticeExecuteReq;
import com.juzifenqi.plus.dto.req.OrderRefundExecuteReq;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 对外输出-处理退卡申请job
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/10 18:06
 */
@Slf4j
@Component
public class PlusRefundApplyJob {

    @Autowired
    private IPlusOrderJobApi plusOrderJobApi;

    /**
     * 对外输出订单通知
     */
    @XxlJob("orderNoticeJob")
    public ReturnT<String> orderNoticeJob(String s) {
        String[] split = s.split(",");
        // 通知类型 1=开卡通知 2=退卡通知
        Integer noticeType = Integer.parseInt(split[0]);
        // 处理状态 1=待处理  4=处理失败，需要重试
        Integer noticeState = Integer.parseInt(split[1]);
        Integer size = Integer.parseInt(split[2]);
        Integer channelId = Integer.parseInt(split[3]);
        OrderNoticeExecuteReq req = new OrderNoticeExecuteReq();
        req.setNoticeType(noticeType);
        req.setNoticeState(noticeState);
        req.setSize(size);
        req.setChannelId(channelId);
        // 重试标识
        if (noticeState == 4) {
            req.setRetry(true);
        }
        plusOrderJobApi.orderNoticeExecute(req);
        return ReturnT.SUCCESS;
    }

    /**
     * 对外输出-处理退卡
     */
    @XxlJob("refundCardJob")
    public ReturnT<String> refundCardJob(String s) {
        String[] split = s.split(",");
        // 处理状态 1=待处理  4=处理失败，需要重试
        Integer dealState = Integer.parseInt(split[0]);
        Integer size = Integer.parseInt(split[1]);
        Integer channelId = Integer.parseInt(split[2]);
        OrderRefundExecuteReq req = new OrderRefundExecuteReq();
        req.setDealState(dealState);
        req.setSize(size);
        req.setChannelId(channelId);
        // 重试标识
        if (dealState == 4) {
            req.setRetry(true);
        }
        plusOrderJobApi.orderRefundExecute(req);
        return ReturnT.SUCCESS;
    }
}

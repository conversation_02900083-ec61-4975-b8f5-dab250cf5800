package com.juzifenqi.plus.job;

import com.groot.utils.exception.LogUtil;
import com.juzifenqi.plus.api.IPlusOrderJobApi;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/4/22  17:30
 * @description
 */
@Slf4j
@Component
public class DssPastMemberRefundRecordJob {

    @Autowired
    private IPlusOrderJobApi jobApi;

    @XxlJob("dssPastMemberRefundRecordJob")
    public ReturnT<String> dssPastMemberRefundRecordJob(String param) {
        try {
            log.info("跑批初始化过期取消记录表新银行卡密文job处理开始:{}", param);
            Long index = 0L;
            Integer batchSize = 1000;
            if (StringUtils.isNotBlank(param)) {
                String[] split = param.split(",");
                index = Long.parseLong(split[0]);
                batchSize = Integer.parseInt(split[1]);
            }
            jobApi.initPastCardNoUuid(index, batchSize);
            log.info("跑批初始化过期取消记录表新银行卡密文job处理结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("跑批初始化过期取消记录表新银行卡密文job处理异常 ", e);
        }
        return ReturnT.FAIL;
    }

    @XxlJob("dssInspectPastMemberRefundRecordJob")
    public ReturnT<String> dssInspectPastMemberRefundRecordJob(String s) {
        try {
            log.info("跑批巡检过期取消记录表新银行卡密文job处理开始:{}", s);
            Integer size = 1000;
            if (StringUtils.isNotBlank(s)) {
                size = Integer.valueOf(s);
            }
            jobApi.inspectPastCardNoUuid(size);
            log.info("跑批巡检过期取消记录表新银行卡密文job处理结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("跑批初始化过期取消记录表新银行卡密文job处理异常 ", e);
        }
        return ReturnT.FAIL;
    }
}

package com.juzifenqi.plus.job;


import com.groot.utils.exception.LogUtil;
import com.juzifenqi.plus.api.IPlusOrderJobApi;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 后台管理系统批量上传会员订单号，系统跑批取消会员订单job
 * <p>走的是无条件取消会员逻辑
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/29 11:22
 */
@Slf4j
@Component
public class PlusOrderBatchCancelJob {

    @Autowired
    private IPlusOrderJobApi jobApi;

    /**
     * 批量取消会员订单job
     */
    @XxlJob("batchCancelOrdersJob")
    public ReturnT<String> batchCancelOrdersJob(String s) {
        try {
            log.info("批量取消会员订单job处理开始");
            jobApi.batchCancelOrders();
            log.info("批量取消会员订单job处理结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("批量取消会员订单job处理异常 ", e);
        }
        return ReturnT.FAIL;
    }
}

package com.juzifenqi.plus.job;

import com.groot.utils.exception.LogUtil;
import com.juzifenqi.plus.api.IPlusOrderJobApi;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 黑名单job
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/5 10:26
 */
@Slf4j
@Component
public class PlusBlackJob {

    @Autowired
    private IPlusOrderJobApi jobApi;

    /**
     * 加入黑名单
     */
    @XxlJob("addPlusBlackTaskJob")
    public ReturnT<String> addPlusBlackTaskJob(String s) {
        try {
            // 默认：待处理
            Integer state = StringUtils.isBlank(s) ? 1 : Integer.parseInt(s);
            log.info("加入黑名单job处理开始：{},{}", state, 50);
            jobApi.executePlusBlackTask(state, 50);
            log.info("加入黑名单job处理结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("加入黑名单job处理异常 ", e);
        }
        return ReturnT.FAIL;
    }
}

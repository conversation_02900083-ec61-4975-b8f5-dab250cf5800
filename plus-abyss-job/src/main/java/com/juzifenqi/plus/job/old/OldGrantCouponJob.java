package com.juzifenqi.plus.job.old;


import com.groot.utils.exception.LogUtil;
import com.juzifenqi.plus.api.IPlusOrderJobApi;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 原super-plus领取优惠券跑批
 * 品牌专区+月享红包
 * 待历史数据跑批完后可以废弃
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/1 13:59
 */
@Slf4j
@Component
public class OldGrantCouponJob {

    @Autowired
    private IPlusOrderJobApi jobApi;

    /**
     * 原super-plus领取优惠券跑批job
     */
    @XxlJob("oldGrantCouponJob")
    public ReturnT<String> oldGrantCouponJob(String s) {
        try {
            log.info("原super-plus领取优惠券跑批job处理开始");
            jobApi.oldGrantCouponJob();
            log.info("原super-plus领取优惠券跑批job处理结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("原super-plus领取优惠券跑批job处理异常 ", e);
        }
        return ReturnT.FAIL;
    }
}

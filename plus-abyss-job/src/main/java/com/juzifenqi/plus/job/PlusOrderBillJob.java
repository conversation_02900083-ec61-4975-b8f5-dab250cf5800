package com.juzifenqi.plus.job;

import com.groot.utils.exception.LogUtil;
import com.juzifenqi.plus.api.IPlusOrderJobApi;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 会员订单对账job
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/18 14:14
 */
@Slf4j
@Component
public class PlusOrderBillJob {

    @Autowired
    private IPlusOrderJobApi jobApi;

    /**
     * 合同签署重试job
     */
    @XxlJob("contractSignRetryJob")
    public ReturnT<String> contractSignRetryJob(String s) {
        try {
            log.info("合同签署重试job处理开始：{}", s);
            Integer size = StringUtils.isNotBlank(s) ? Integer.parseInt(s) : 50;
            jobApi.contractSignReTry(size);
            log.info("合同签署重试job处理结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("合同签署重试job处理异常 ", e);
        }
        return ReturnT.FAIL;
    }

    /**
     * 合同上传重试job
     */
    @XxlJob("contractUploadRetryJob")
    public ReturnT<String> contractUploadRetryJob(String s) {
        try {
            log.info("合同上传重试job处理开始：{}", s);
            Integer size = StringUtils.isNotBlank(s) ? Integer.parseInt(s) : 50;
            jobApi.contractUploadReTry(size);
            log.info("合同上传重试job处理结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("合同上传重试job处理异常 ", e);
        }
        return ReturnT.FAIL;
    }

    /**
     * 通知三方入账重试job
     */
    @XxlJob("incomeNotifyRetryJob")
    public ReturnT<String> incomeNotifyRetryJob(String s) {
        try {
            log.info("通知三方入账重试job处理开始：{}", s);
            Integer size = StringUtils.isNotBlank(s) ? Integer.parseInt(s) : 50;
            jobApi.incomeRetry(size);
            log.info("通知三方入账重试job处理结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("通知三方入账重试job处理异常 ", e);
        }
        return ReturnT.FAIL;
    }

    /**
     * 通知三方出账重试job
     */
    @XxlJob("outcomeNotifyRetryJob")
    public ReturnT<String> outcomeNotifyRetryJob(String s) {
        try {
            log.info("通知三方出账重试job处理开始：{}", s);
            Integer size = StringUtils.isNotBlank(s) ? Integer.parseInt(s) : 50;
            jobApi.outcomeRetry(size);
            log.info("通知三方出账重试job处理结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("通知三方出账重试job处理异常 ", e);
        }
        return ReturnT.FAIL;
    }

    /**
     * 划扣中数据重试
     */
    @XxlJob("deductIncomeRetryJob")
    public ReturnT<String> deductIncomeRetryJob(String s) {
        try {
            //默认执行100条
            Integer size = StringUtils.isBlank(s) ? 100 : Integer.parseInt(s);
            log.info("----对账表划扣中数据重试--job开始----");
            jobApi.deductIncomeRetry(size);
            log.info("----对账表划扣中数据重试--job结束----");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("对账表划扣中数据重试异常 ", e);
        }
        return ReturnT.FAIL;
    }
}

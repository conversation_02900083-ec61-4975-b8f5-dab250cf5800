package com.juzifenqi.plus.job;


import com.groot.utils.exception.LogUtil;
import com.juzifenqi.plus.api.IPlusOrderJobApi;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 代付打款job
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/28 18:06
 */
@Slf4j
@Component
public class PlusDefrayJob {

    @Autowired
    private IPlusOrderJobApi jobApi;

    /**
     * 代付打款job
     */
    @XxlJob("orderDefrayJob")
    public ReturnT<String> orderDefrayJob(String s) {
        try {
            log.info("代付打款job处理开始");
            jobApi.orderDefrayExecute();
            log.info("代付打款job处理结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("代付打款job处理异常 ", e);
        }
        return ReturnT.FAIL;
    }
}

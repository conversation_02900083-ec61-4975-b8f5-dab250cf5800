package com.juzifenqi.plus.job;


import com.groot.utils.exception.LogUtil;
import com.juzifenqi.plus.api.IPlusOrderJobApi;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 会员过期
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/28 18:06
 */
@Slf4j
@Component
public class MemberExpireJob {

    @Autowired
    private IPlusOrderJobApi jobApi;

    /**
     * 会员过期
     */
    @XxlJob("memberExpireJob")
    public ReturnT<String> memberExpireJob(String s) {
        try {
            log.info("会员过期job处理开始");
            jobApi.memberExpire();
            log.info("会员过期job处理结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("会员过期job处理异常 ", e);
        }
        return ReturnT.FAIL;
    }
}

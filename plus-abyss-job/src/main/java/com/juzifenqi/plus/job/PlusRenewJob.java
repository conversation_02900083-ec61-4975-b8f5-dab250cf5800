package com.juzifenqi.plus.job;

import com.juzifenqi.plus.api.IPlusOrderJobApi;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 续费job
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/26 16:53
 */
@Component
@Slf4j
public class PlusRenewJob {

    @Autowired
    private IPlusOrderJobApi plusOrderJobApi;

    /**
     * 小额月卡续费计划续费job
     */
    @XxlJob("plusOrderRenewJob")
    public ReturnT<String> renewExecute(String s) {
        plusOrderJobApi.renewJob(20);
        return ReturnT.SUCCESS;
    }

    /**
     * 小额月卡续费计划续费job
     */
    @XxlJob("plusOrderRenewRetryJob")
    public ReturnT<String> renewRetryJobExecute(String s) {
        plusOrderJobApi.renewJobRetry(20, 3);
        return ReturnT.SUCCESS;
    }
}

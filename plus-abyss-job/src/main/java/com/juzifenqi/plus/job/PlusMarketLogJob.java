package com.juzifenqi.plus.job;

import com.groot.utils.exception.LogUtil;
import com.juzifenqi.plus.api.IPlusMarketingLogApi;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 会员营销日志job
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/5 10:26
 */
@Slf4j
@Component
public class PlusMarketLogJob {

    @Resource
    private IPlusMarketingLogApi marketingLogApi;

    private static final Integer DEFAULT_BEFORE_DAYS = 30;
    private static final Integer DEFAULT_LIMIT = 100;

    /**
     * 会员营销日志记录迁移
     */
    @XxlJob("marketLogMigrateJob")
    public ReturnT<String> plusMarketLogMigrate(String s) {
        log.info("----会员营销日志迁移--job开始----");
        try {
            int beforeDays = DEFAULT_BEFORE_DAYS;
            int limit = DEFAULT_LIMIT;
            if (StringUtils.isNotBlank(s) && s.split(",").length == 2) {
                try {
                    beforeDays = Integer.parseInt(s.split(",")[0]);
                    limit = Integer.parseInt(s.split(",")[1]);
                } catch (Exception e) {
                    log.error("会员营销日志迁移任务参数解析异常,error", e);
                }
            }
            marketingLogApi.marketLogMigrate(beforeDays, limit);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("会员营销日志迁移异常", e);
        }
        log.info("----会员营销日志迁移--job结束----");
        return ReturnT.FAIL;
    }
}

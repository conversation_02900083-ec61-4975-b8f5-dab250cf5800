package com.juzifenqi.plus.job;

import com.groot.utils.exception.LogUtil;
import com.juzifenqi.plus.api.IPlusShuntJobApi;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/4/22  17:30
 * @description
 */
@Slf4j
@Component
public class DssPlusShuntJob {

    @Autowired
    private IPlusShuntJobApi jobApi;

    @XxlJob("dssPlusShuntSupplierPayJob")
    public ReturnT<String> dssPlusShuntSupplierPayJob(String s) {
        try {
            log.info("跑批初始化分流主体支付配置新银行卡密文job处理开始:{}", s);
            Integer size = 1000;
            if (StringUtils.isNotBlank(s)) {
                size = Integer.valueOf(s);
            }
            jobApi.initSupplierBankAccountNoUuid(size);
            log.info("跑批初始化分流主体支付配置新银行卡密文job处理结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("跑批初始化分流主体支付配置新银行卡密文job处理异常", e);
        }
        return ReturnT.FAIL;
    }

    @XxlJob("dssInspectPlusShuntSupplierPayJob")
    public ReturnT<String> dssInspectPlusShuntSupplierPayJob(String s) {
        try {
            log.info("跑批巡检分流主体支付配置新银行卡密文job处理开始:{}", s);
            Integer size = 1000;
            if (StringUtils.isNotBlank(s)) {
                size = Integer.valueOf(s);
            }
            jobApi.inspectSupplierBankAccountNoUuid(size);
            log.info("跑批巡检分流主体支付配置新银行卡密文job处理结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("跑批巡检分流主体支付配置新银行卡密文job处理异常", e);
        }
        return ReturnT.FAIL;
    }
}

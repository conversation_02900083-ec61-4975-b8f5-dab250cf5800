package com.juzifenqi.plus.job;

import com.juzifenqi.plus.api.IPlusOrderJobApi;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 该定时任务用于处理
 * 补签2024.07-2024.09期间由于
 * 业务在合同系统配置原因，导致开通融单咨询卡但是未签署合同的订单
 * <AUTHOR>
 * @date 2024/10/10 11:10
 */
@Component
@Slf4j
public class ManualHandleRdzxOrderContractJob {

    @Autowired
    private IPlusOrderJobApi orderJobApi;

    @XxlJob("manualHandleRdzxOrderContractJob")
    public ReturnT<String> manualHandleRdzxOrderContractJob(String param){
        orderJobApi.manualHandleRdzxOrderContractJob();
        return ReturnT.SUCCESS;
    }



    @XxlJob("makeUpHandleRdzxOrderContractJob")
    public ReturnT<String> makeUpHandleRdzxOrderContractJob(String param){
        orderJobApi.makeUpHandleRdzxOrderContractJob();
        return ReturnT.SUCCESS;
    }


}

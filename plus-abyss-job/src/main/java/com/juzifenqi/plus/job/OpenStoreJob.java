package com.juzifenqi.plus.job;


import com.groot.utils.exception.LogUtil;
import com.juzifenqi.plus.api.IPlusOrderJobApi;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * OpenStoreJob 
 * <AUTHOR> 
 * @Description 桔会卡-购物返现-开店重试job
 * @Date 2024/06/24 14:34
**/
@Component
@Slf4j
public class OpenStoreJob {

    @Autowired
    private IPlusOrderJobApi orderJobApi;

    /**
     * 开店重试
     */
    @XxlJob("openStoreRetryJob")
    public ReturnT<String> execute(String s) {
        try {
            log.info("开店重试开始");
            orderJobApi.openStoreRetry();
            log.info("开店重试结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("开店重试异常 ", e);
        }
        return ReturnT.FAIL;
    }
}

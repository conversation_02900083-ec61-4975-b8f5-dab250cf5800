package com.juzifenqi.plus.job;


import com.groot.utils.exception.LogUtil;
import com.juzifenqi.plus.api.IPlusOrderJobApi;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 结清返现job
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/13 10:23
 */
@Slf4j
@Component
public class PlusMicroDefrayJob {

    @Autowired
    private IPlusOrderJobApi jobApi;

    /**
     * 结清返现job
     */
    @XxlJob("microDefrayPayJob")
    public ReturnT<String> microDefrayPayJob(String s) {
        try {
            log.info("结清返现job处理开始");
            jobApi.microDefrayPayExecute();
            log.info("结清返现job处理结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("结清返现job处理异常 ", e);
        }
        return ReturnT.FAIL;
    }

    /**
     * 还款返现job
     */
    @XxlJob("hkfxMicroDefrayPayJob")
    public ReturnT<String> hkfxMicroDefrayPayJob(String s) {
        try {
            log.info("还款返现job处理开始");
            jobApi.hkfxMicroDefrayPayJob();
            log.info("还款返现job处理结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("还款返现job处理异常 ", e);
        }
        return ReturnT.FAIL;
    }

}

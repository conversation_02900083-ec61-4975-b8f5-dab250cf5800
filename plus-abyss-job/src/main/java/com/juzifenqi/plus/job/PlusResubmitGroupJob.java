package com.juzifenqi.plus.job;


import com.groot.utils.exception.LogUtil;
import com.juzifenqi.plus.api.IPlusOrderJobApi;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.util.ShardingUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * PlusResubmitGroupJob 
 * <AUTHOR> 
 * @Description 重提客群Job 
 * @Date 2024/06/24 14:33
**/
@Slf4j
@Component
public class PlusResubmitGroupJob {

    @Autowired
    private IPlusOrderJobApi orderJobApi;

    /**
     * 重提客群主流程失败重试job，分片
     */
    @XxlJob("resubmitGroupRetryJob")
    public ReturnT<String> executeMainRetry(String s) {
        try {
            Integer size = StringUtils.isBlank(s) ? 20 : Integer.parseInt(s);
            ShardingUtil.ShardingVO shardingVO = ShardingUtil.getShardingVo();
            //执行器数量
            int number = shardingVO.getTotal();
            //当前分片  index
            int index = shardingVO.getIndex();
            log.info("重提客群主流程失败重试job参数：{},执行器数量：{},当前分片index：{}", s, number,
                    index);
            log.info("----重提客群主流程失败重试开始--job开始----");
            orderJobApi.resubmitGroupRetry(size, index);
            log.info("----重提客群主流程失败重试结束--job结束----");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("重提客群主流程失败重试异常 ", e);
        }
        return ReturnT.FAIL;
    }

    /**
     * 重提客群点击放弃借款按钮后，资匹闭单失败重试job
     */
    @XxlJob("resubmitGroupZpCloseRetryJob")
    public ReturnT<String> execute(String s) {
        try {
            log.info("重提客群点击放弃借款按钮后资匹闭单失败重试开始：{}", s);
            orderJobApi.resubmitGroupZpCloseRetry(
                    StringUtils.isBlank(s) ? 100 : Integer.parseInt(s));
            log.info("重提客群点击放弃借款按钮后资匹闭单失败重试结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("重提客群点击放弃借款按钮后资匹闭单失败重试异常 ", e);
        }
        return ReturnT.FAIL;
    }
}

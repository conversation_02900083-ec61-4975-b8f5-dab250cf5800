package com.juzifenqi.plus.job;


import com.groot.utils.exception.LogUtil;
import com.juzifenqi.plus.api.IPlusOrderJobApi;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 权益发放计划任务job
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/27 14:13
 */
@Slf4j
@Component
public class PlusSendPlanTaskJob {

    @Autowired
    private IPlusOrderJobApi jobApi;

    /**
     * 权益发放计划任务job
     * <p>入参可为modelId或者为空</p>
     */
    @XxlJob("plusSendPlanTaskJob")
    public ReturnT<String> plusSendPlanTaskJob(String s) {
        try {
            log.info("权益发放计划任务job处理开始");
            Integer modelId = StringUtils.isNotBlank(s) ? Integer.parseInt(s) : null;
            jobApi.plusSendPlanTasks(modelId);
            log.info("权益发放计划任务job处理结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("权益发放计划任务job处理异常 ", e);
        }
        return ReturnT.FAIL;
    }
}

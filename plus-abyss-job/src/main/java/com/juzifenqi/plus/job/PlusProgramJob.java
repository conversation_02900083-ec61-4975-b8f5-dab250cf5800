package com.juzifenqi.plus.job;

import com.groot.utils.exception.LogUtil;
import com.juzifenqi.plus.api.IPlusOrderJobApi;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 方案job
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/5 10:26
 */
@Slf4j
@Component
public class PlusProgramJob {

    @Autowired
    private IPlusOrderJobApi jobApi;

    /**
     * 方案改价
     */
    @XxlJob("editProgramPriceJob")
    public ReturnT<String> editProgramPriceJob(String s) {
        try {
            log.info("方案改价job处理开始");
            jobApi.plusProgramEditPrice();
            log.info("方案改价job处理结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("方案改价job处理异常 ", e);
        }
        return ReturnT.FAIL;
    }
}

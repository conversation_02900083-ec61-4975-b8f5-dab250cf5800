package com.juzifenqi.plus.job.settle;

import com.groot.utils.exception.LogUtil;
import com.juzifenqi.plus.api.ISettleJobApi;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/4/22  16:11
 * @description
 */
@Slf4j
@Component
public class DssSettleBillApplyJob {

    @Autowired
    private ISettleJobApi jobApi;

    @XxlJob("dssSettleBillApplyJob")
    public ReturnT<String> dssSettleBillApplyJob(String s) {
        try {
            log.info("跑批初始化结算单申请表新银行卡密文job处理开始:{}", s);
            Integer size = 1000;
            if (StringUtils.isNotBlank(s)) {
                size = Integer.valueOf(s);
            }
            jobApi.initSettleBankAccountNoUuid(size);
            log.info("跑批初始化结算单申请表新银行卡密文job处理结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("初始化结算单申请表新银行卡密文job处理异常", e);
        }
        return ReturnT.FAIL;
    }

    @XxlJob("dssInspectSettleBillApplyJob")
    public ReturnT<String> dssInspectSettleBillApplyJob(String s) {
        try {
            log.info("跑批巡检结算单申请表新银行卡密文job处理开始:{}", s);
            Integer size = 1000;
            if (StringUtils.isNotBlank(s)) {
                size = Integer.valueOf(s);
            }
            jobApi.inspectSettleBankAccountNoUuid(size);
            log.info("跑批巡检结算单申请表新银行卡密文job处理结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("初始化结算单申请表新银行卡密文job处理异常", e);
        }
        return ReturnT.FAIL;
    }
}

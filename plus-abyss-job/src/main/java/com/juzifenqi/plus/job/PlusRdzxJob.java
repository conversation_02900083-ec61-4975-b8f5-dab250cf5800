package com.juzifenqi.plus.job;


import com.groot.utils.exception.LogUtil;
import com.juzifenqi.plus.api.IPlusOrderJobApi;
import com.juzifenqi.plus.enums.PlusPayTypeEnum;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 融担咨询卡job
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/28 18:06
 */
@Slf4j
@Component
public class PlusRdzxJob {

    @Autowired
    private IPlusOrderJobApi jobApi;

    /**
     * 融担咨询卡创单
     */
    @XxlJob("createRdzxOrder")
    public ReturnT<String> createRdzxOrder(String s) {
        try {
            log.info("融担咨询卡创单job处理开始：{}", s);
            Integer optStatus = StringUtils.isNotBlank(s) ? Integer.parseInt(s) : 0;
            jobApi.batchCreateRdzxOrder(optStatus);
            log.info("融担咨询卡创单job处理结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("融担咨询卡创单job处理异常 ", e);
        }
        return ReturnT.FAIL;
    }

    /**
     * 融担咨询卡划付日划扣
     */
    @XxlJob("repayDateDeduct")
    public ReturnT<String> repayDateDeduct(String s) {
        try {
            log.info("融担咨询卡划扣日划扣job处理开始");
            jobApi.rdzxDeduct(PlusPayTypeEnum.PAY_TYPE_5);
            log.info("融担咨询卡划扣日划扣job处理结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("融担咨询卡划扣日划扣job处理异常 ", e);
        }
        return ReturnT.FAIL;
    }

    /**
     * 融担咨询卡逾期划扣
     */
    @XxlJob("overdueDeduct")
    public ReturnT<String> overdueDeduct(String s) {
        try {
            log.info("融担咨询卡逾期划扣job处理开始");
            jobApi.rdzxDeduct(PlusPayTypeEnum.PAY_TYPE_6);
            log.info("融担咨询卡逾期划扣job处理结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("融担咨询卡逾期划扣job处理异常 ", e);
        }
        return ReturnT.FAIL;
    }

    /**
     * 拉取还款计划
     * <p>融担卡在开通会员时会拉取还款计划，如果当时还款计划不存在的话，则需要 job 跑批再次重新拉取
     */
    @XxlJob("repayPlanJob")
    public ReturnT<String> repayPlanJob(String s) {
        try {
            log.info("拉取还款计划开始");
            jobApi.repayPlanJob();
            log.info("拉取还款计划结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("拉取还款计划异常 ", e);
        }
        return ReturnT.FAIL;
    }

}

package com.juzifenqi.plus.job;


import com.groot.utils.exception.LogUtil;
import com.juzifenqi.plus.api.IPlusOrderJobApi;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * PlusRepayRemindSmsJob 
 * <AUTHOR> 
 * @Description 还款卡结束发短信Job 
 * @Date 2024/06/24 14:29
**/
@Component
@Slf4j
public class PlusRepayRemindSmsJob {

    @Autowired
    private IPlusOrderJobApi orderJobApi;

    /**
     * 还款短信提醒
     */
    @XxlJob("repayPlusRemindSmsJob")
    public ReturnT<String> execute(String s) {
        try {
            log.info("还款短信提醒开始");
            orderJobApi.repayPlusRemindSms(s);
            log.info("还款短信提醒结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("还款短信提醒异常 ", e);
        }
        return ReturnT.FAIL;
    }
}

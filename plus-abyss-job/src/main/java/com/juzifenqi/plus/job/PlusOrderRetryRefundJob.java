package com.juzifenqi.plus.job;

import com.juzifenqi.plus.api.IPlusOrderJobApi;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class PlusOrderRetryRefundJob {

    @Autowired
    private IPlusOrderJobApi jobApi;

    /**
     * 会员订单重新退款（用于首付支付订单退款第一次成功，第二次后进行重试）
     */
    @XxlJob("plusOrderRetryRefund")
    public ReturnT<String> plusOrderRetryRefund(String s) {

        if (StringUtils.isNotBlank(s)) {
            jobApi.orderRetryRefund(Long.parseLong(s));
            return ReturnT.SUCCESS;
        }
        return ReturnT.FAIL;

    }

}

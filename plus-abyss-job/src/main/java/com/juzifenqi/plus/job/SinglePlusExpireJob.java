package com.juzifenqi.plus.job;


import com.groot.utils.exception.LogUtil;
import com.juzifenqi.plus.api.IPlusOrderJobApi;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * SinglePlusExpireJob 
 * <AUTHOR> 
 * @Description 单会员过期任务
 * @Date 2024/06/24 14:35
**/
@Component
@Slf4j
public class SinglePlusExpireJob {

    @Autowired
    private IPlusOrderJobApi orderJobApi;

    /**
     * 单会员过期任务
     */
    @XxlJob("singlePlusExpire")
    public ReturnT<String> execute(String s) {
        try {
            log.info("单会员过期任务开始");
            orderJobApi.singlePlusExpire();
            log.info("单会员过期任务结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("单会员过期任务异常 ", e);
        }
        return ReturnT.FAIL;
    }
}

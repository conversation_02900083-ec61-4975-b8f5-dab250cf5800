package com.juzifenqi.plus.job;


import com.groot.utils.exception.LogUtil;
import com.juzifenqi.plus.api.IPlusOrderJobApi;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * PlusGetFlagJob 
 * <AUTHOR> 
 * @Description 重提订单标识查询Job 
 * @Date 2024/06/24 14:31
**/
@Slf4j
@Component
public class PlusGetFlagJob {

    @Autowired
    private IPlusOrderJobApi orderJobApi;

    @XxlJob("resubmitFlagVerifyHandler")
    public ReturnT<String> execute(String s) {
        try {
            log.info("----订单重提标识查询--job开始----");
            orderJobApi.resubmitFlagVerify();
            log.info("---订单重提标识查询--job结束----");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("订单重提标识查询处理异常 ", e);
        }
        return ReturnT.FAIL;
    }
}

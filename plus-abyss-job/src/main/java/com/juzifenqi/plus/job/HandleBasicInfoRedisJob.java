package com.juzifenqi.plus.job;


import com.juzifenqi.plus.api.IPlusOrderJobApi;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * HandleBasicInfoRedisJob 
 * <AUTHOR> 
 * @Description  
 * @Date 2024/06/18 09:32
**/
@Slf4j
@Component
public class HandleBasicInfoRedisJob    {

    @Autowired
    private IPlusOrderJobApi plusOrderJobApi;

    @XxlJob("handleBasicInfoRedis")
    public ReturnT<String> execute(String s) {
        try {
            log.info("定时处理方案ID和方案基础权益缓存任务开始");
            plusOrderJobApi.handlePlusBasicProfitsCache();
            log.info("定时处理方案ID和方案基础权益缓存任务结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.info("定时处理方案ID和方案基础权益缓存任务异常 ", e);
        }
        return ReturnT.FAIL;
    }
}

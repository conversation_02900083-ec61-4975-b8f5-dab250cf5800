package com.juzifenqi.plus.job.settle;

import com.groot.utils.core.date.DateUtils;
import com.juzifenqi.plus.api.ISettleJobApi;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 结算单发起代付
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/6 10:55
 */
@Slf4j
@Component
public class DefraySettleBillJob {

    @Autowired
    private ISettleJobApi settleJobApi;

    /**
     * T+1每天早上9点跑批结算单发起代付
     */
    @XxlJob("defraySettleBillJob")
    public ReturnT<String> defraySettleBillJob(String s) {
        log.info("跑批结算单发起代付job处理开始");
        // 默认是当天
        String date = DateUtils.getSysCurFmtDate();
        if (StringUtils.isNotBlank(s)) {
            date = s;
        }
        settleJobApi.defraySettleBillJob(date);
        log.info("跑批结算单发起代付job处理结束");
        return ReturnT.SUCCESS;
    }
}

package com.juzifenqi.plus.job;


import com.groot.utils.exception.LogUtil;
import com.juzifenqi.plus.api.IPlusOrderJobApi;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 非融担咨询卡划扣job
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/28 18:06
 */
@Slf4j
@Component
public class PlusOrderDeductJob {

    @Autowired
    private IPlusOrderJobApi jobApi;

    /**
     * 非融担咨询卡延迟划扣
     */
    @XxlJob("delayDeduct")
    public ReturnT<String> delayDeduct(String s) {
        try {
            log.info("非融担咨询卡延迟划扣job处理开始");
            jobApi.delayBatchDeduct();
            log.info("非融担咨询卡延迟划扣job处理结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("非融担咨询卡延迟划扣job处理异常 ", e);
        }
        return ReturnT.FAIL;
    }

    /**
     * 非融担咨询卡延迟划扣短信提醒
     */
    @XxlJob("delayDeductSms")
    public ReturnT<String> delayDeductSms(String s) {
        try {
            log.info("非融担咨询卡延迟划扣短信提醒job处理开始");
            jobApi.delayDeductSms();
            log.info("非融担咨询卡延迟划扣短信提醒job处理结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("非融担咨询卡延迟划扣短信提醒job处理异常 ", e);
        }
        return ReturnT.FAIL;
    }

    /**
     * 划扣失败预警
     */
    @XxlJob("deductError")
    public ReturnT<String> deductError(String param) {
        try {
            log.info("划扣失败预警开始");
            jobApi.deductError(param);
            log.info("划扣失败预警结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("划扣失败预警异常:", e);
        }
        return ReturnT.FAIL;
    }

    /**
     * 指定订单、会员类型、方案等进行代扣(手动调用,调用前请检查数据)
     */
    @XxlJob("manualDeduct")
    public ReturnT<String> manualDeduct(String param) {
        try {
            log.info("手动划扣开始");
            jobApi.manualDeduct(param);
            log.info("手动划扣结束");
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            LogUtil.printLog("划扣失败预警异常:", e);
        }
        return ReturnT.FAIL;
    }

}

package com.juzifenqi.plus.job.settle;

import com.groot.utils.core.date.DateUtils;
import com.juzifenqi.plus.api.ISettleJobApi;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 生成结算单
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/5 16:57
 */
@Slf4j
@Component
public class CreateSettleBillJob {

    @Autowired
    private ISettleJobApi settleJobApi;

    /**
     * T+1每天凌晨跑批生成结算单
     */
    @XxlJob("createSettleBillJob")
    public ReturnT<String> createSettleBillJob(String s) {
        log.info("跑批生成结算单job处理开始");
        // 默认是昨天
        String beginTime = DateUtils.getYesterdayStr();
        String endTime = DateUtils.getYesterdayStr();
        if (StringUtils.isNotBlank(s)) {
            String[] split = s.split(",");
            beginTime = split[0];
            endTime = split[1];
        }
        beginTime += " 00:00:00";
        endTime += " 23:59:59";
        settleJobApi.createSettleBillJob(beginTime, endTime);
        log.info("跑批生成结算单job处理结束");
        return ReturnT.SUCCESS;
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <jmxConfigurator/>

    <property name="log_home" value="/data/log/plus-abyss-job"/>

    <define name="hostName" class="com.juzifenqi.plus.utils.LogBackUtils"/>

    <appender name="rollingFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log_home}/log-${hostName}.%d{yyyy-MM-dd.HH}.log</fileNamePattern>
        </rollingPolicy>
        <encoder charset="UTF-8" class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %X{traceId} - [%tid] ^_^ %msg%n</pattern>
            </layout>
        </encoder>
    </appender>

    <!-- project default level -->
    <logger name="com.juzifenqi" level="INFO"/>

    <!-- root -->
    <root level="INFO">
        <appender-ref ref="rollingFile"/>
    </root>

    <!-- 下面配置一些第三方包的日志过滤级别，用于避免刷屏 -->
    <logger name="org.springframework.jndi" level="WARN"/>
    <logger name="org.springframework.scheduling" level="WARN"/>
    <logger name="org.apache" level="WARN"/>
    <logger name="org.eclipse" level="WARN"/>
    <logger name="io.lettuce.core" level="WARN"/>
</configuration>
## 项目整体架构，采用DDD架构设计

```angular2html
plus-abyss/                         # 项目根目录
├── pom.xml                         # 父POM，定义项目依赖和模块
├── plus-abyss-domain/              # 领域模块，包含核心业务逻辑
├── plus-abyss-api/                 # API模块，定义对外接口
├── plus-abyss-config/              # 配置模块，统一配置管理
├── plus-abyss-external/            # 外部集成模块，处理与外部系统交互
└── plus-abyss-job/                 # 定时任务模块，独立可部署
```

## 模块详细结构

```angular2html


plus-abyss-domain/
├── pom.xml                         # 模块POM
├── src/
│   └── main/
│       ├── java/
│       │   └── com/
│       │       └── juzifenqi/
│       │           └── plus/
│       │               ├── PlusAbyssApplication.java  # 主应用入口类
│       │               ├── MonitorController.java     # 监控控制器
│       │               ├── module/                    # 业务模块
│       │               │   └── [业务子模块]/
│       │               │       ├── application/       # 应用层
│       │               │       ├── model/             # 领域能力定义
│       │               │       │   ├── impl/          # 领域能力实现
│       │               │       │   ├── event/         # 事件层
│       │               │       │   └── contract/      # 基础设施契约层(SPI)
│       │               │       │       ├── entity/    # 实体层
│       │               │       │       └── external/  # 外部交互防腐定义层 （周边系统调用实现层）
│       │               │       └── repository/        # 仓储层
│       │               │           ├── impl/          # 基础设施实现层
│       │               │           ├── dao/           # MySQL持久化层
│       │               │           ├── external/      # 外部系统集成
│       │               │           │   └── acl/       # 外部系统防腐层
│       │               │           └── po/            # 持久化对象层 ，与数据库表一一对应
│       │               ├── mq/                        # 消息队列相关组件
│       │               ├── utils/                     # 工具类
│       │               ├── exception/                 # 异常处理
│       │               ├── init/                      # 初始化相关
│       │               └── aspect/                    # 切面（AOP）
│       └── resources/                                 # 资源文件目录
└── target/                                            # 编译输出目录

plus-abyss-api/
├── pom.xml                         # 模块POM
├── src/
│   └── main/
│       └── java/
│           └── com/
│               └── juzifenqi/
│                   └── plus/
│                       ├── api/                        # 接口定义
│                       ├── dto/                        # 数据传输对象
│                       └── enums/                      # 枚举类型
└── target/                                             # 编译输出目录

plus-abyss-config/
├── pom.xml                         # 模块POM
├── src/
│   └── main/
│       └── java/
│           └── com/
│               └── juzifenqi/
│                   └── plus/
│                       ├── config/                     # 配置类
│                       ├── constants/                  # 常量定义
│                       ├── dubbo/                      # Dubbo相关配置
│                       ├── pool/                       # 资源池配置
│                       └── enums/                      # 枚举类型
└── target/                                             # 编译输出目录

plus-abyss-external/
├── pom.xml                         # 模块POM
├── src/
│   └── main/
│       └── java/
│           └── com/
│               └── juzifenqi/
│                   └── plus/
│                       └── external/                   # 外部系统集成实现
└── target/                                             # 编译输出目录

plus-abyss-job/
├── pom.xml                         # 模块POM
├── src/
│   └── main/
│       └── java/
│           └── com/
│               └── juzifenqi/
│                   └── plus/
│                       ├── PlusAbyssJobApplication.java  # 定时任务应用入口类
│                       ├── MonitorController.java        # 监控控制器
│                       ├── job/                          # 定时任务实现
│                       ├── utils/                        # 工具类
│                       └── config/                       # 配置类
└── target/                                               # 编译输出目录
```

## 各模块职责和关系

### plus-abyss-domain：
- 作为核心模块，包含业务逻辑和领域模型
- 实现领域驱动设计(DDD)中的领域层和应用层
- 提供了完整的业务功能实现

### plus-abyss-api：
- 定义对外暴露的服务接口和数据传输对象
- 实现接口与实现分离，便于系统集成和版本管理
- 被其他系统或模块依赖，实现服务调用

### plus-abyss-config：
- 统一管理系统配置，包括常量、枚举和各种组件配置
- 包含Dubbo、线程池等配置，方便统一管理和调整

### plus-abyss-external：
- 负责与外部系统的集成，实现系统间的交互
- 通过防腐层设计模式，隔离外部系统变化对核心业务的影响
- plus-abyss-job：

### plus-abyss-job
- 包含系统的定时任务，有独立的应用入口
- 可以独立部署和运行，提高系统可伸缩性和灵活性

## 核心表

``` angular2html
plus_order_info 会员订单
plus_order_ext_info 会员订单扩展表 
member_plus_info 付费会员周期表 
member_plus_info_detail 会员业务订单
plus_program 会员方案
plus_program_price 会员差异化方案
plus_market_chain 营销流程
```